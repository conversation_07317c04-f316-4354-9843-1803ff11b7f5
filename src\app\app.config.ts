import {
  ApplicationConfig,
  importProviders<PERSON>rom,
  inject,
  provideAppInitializer,
  provideBrowserGlobalErrorListeners,
  provideZonelessChangeDetection,
} from '@angular/core';
import { provideRouter, withPreloading } from '@angular/router';

import { DatePipe, registerLocaleData } from '@angular/common';
import {
  HttpClient,
  provideHttpClient,
  withFetch,
  withInterceptors,
} from '@angular/common/http';
import it from '@angular/common/locales/it';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { errorInterceptor } from '@core/interceptor/error.interceptor';
import { jwtInterceptor } from '@core/interceptor/jwt-interceptor';
import { requestInterceptor } from '@core/interceptor/request.interceptor';
import { uploadInterceptor } from '@core/interceptor/upload-interceptor';
import { FlagBasedPreloadingStrategy } from '@core/services/flag-based.preloading-strategy';
import { InitializeService } from '@core/services/initialize';
import {
  provideTranslateService,
  TranslateLoader,
  TranslateService,
} from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { it_IT, provideNzI18n } from 'ng-zorro-antd/i18n';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NGX_EDITOR_CONFIG_TOKEN } from 'ngx-editor';
import { routes } from './app.routes';
import { DateFormatPipe } from './core/pipes/date.pipe';
import { provideNzIcons } from './icons-provider';

registerLocaleData(it);

export const appConfig: ApplicationConfig = {
  providers: [
    DatePipe,
    DateFormatPipe,
    provideAppInitializer(() => {
      const initializeService = inject(InitializeService);
      return initializeService.initConfigApp();
    }),
    provideBrowserGlobalErrorListeners(),
    provideZonelessChangeDetection(),
    provideRouter(routes, withPreloading(FlagBasedPreloadingStrategy)),
    provideHttpClient(
      withInterceptors([
        uploadInterceptor,
        requestInterceptor,
        errorInterceptor,
        jwtInterceptor,
      ]),
      withFetch(),
    ),
    provideNzIcons(),
    provideNzI18n(it_IT),
    importProvidersFrom(NzModalModule),
    provideAnimationsAsync(),
    provideTranslateService({
      loader: {
        provide: TranslateLoader,
        useFactory: TranslateLoaderFn,
        deps: [HttpClient],
      },
    }),
    provideNgxEditorConfig(),
  ],
};

export function TranslateLoaderFn(http: HttpClient) {
  return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}

export function provideNgxEditorConfig() {
  return {
    provide: NGX_EDITOR_CONFIG_TOKEN,
    useFactory: () => ({
      translateService: inject(TranslateService),
      
      locals: {
        // Traduzioni per l'interfaccia dell'editor
        'Insert Link': translateService.instant('EDITOR.insertLink'),
        Insert: translateService.instant('EDITOR.insert'),
        URL: translateService.instant('EDITOR.url'),
        Text: translateService.instant('EDITOR.text'),
        'Open in new tab': translateService.instant('EDITOR.openInNewTab'),
        Heading: translateService.instant('EDITOR.heading'),
        Bold: translateService.instant('EDITOR.bold'),
        Italic: translateService.instant('EDITOR.italic'),
        Underline: translateService.instant('EDITOR.underline'),
        Strike: translateService.instant('EDITOR.strike'),
        Blockquote: translateService.instant('EDITOR.blockquote'),
        'Ordered List': translateService.instant('EDITOR.orderedList'),
        'Bullet List': translateService.instant('EDITOR.bulletList'),
        Link: translateService.instant('EDITOR.link'),
        'Align Left': translateService.instant('EDITOR.alignLeft'),
        'Align Center': translateService.instant('EDITOR.alignCenter'),
        'Align Right': translateService.instant('EDITOR.alignRight'),
      },
    }),
    deps: [TranslateService],
  };
}
