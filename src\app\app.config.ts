import {
  ApplicationConfig,
  importProviders<PERSON>rom,
  inject,
  provideAppInitializer,
  provideBrowserGlobalErrorListeners,
  provideZonelessChangeDetection,
} from '@angular/core';
import { provideRouter, withPreloading } from '@angular/router';

import { DatePipe, registerLocaleData } from '@angular/common';
import {
  HttpClient,
  provideHttpClient,
  withFetch,
  withInterceptors,
} from '@angular/common/http';
import it from '@angular/common/locales/it';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { errorInterceptor } from '@core/interceptor/error.interceptor';
import { jwtInterceptor } from '@core/interceptor/jwt-interceptor';
import { requestInterceptor } from '@core/interceptor/request.interceptor';
import { uploadInterceptor } from '@core/interceptor/upload-interceptor';
import { FlagBasedPreloadingStrategy } from '@core/services/flag-based.preloading-strategy';
import { InitializeService } from '@core/services/initialize';
import { provideTranslateService, TranslateLoader } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { it_IT, provideNzI18n } from 'ng-zorro-antd/i18n';
import { NzModalModule } from 'ng-zorro-antd/modal';

import { routes } from './app.routes';
import { DateFormatPipe } from './core/pipes/date.pipe';
import { provideNzIcons } from './icons-provider';

registerLocaleData(it);

export const appConfig: ApplicationConfig = {
  providers: [
    DatePipe,
    DateFormatPipe,
    provideAppInitializer(() => {
      const initializeService = inject(InitializeService);
      return initializeService.initConfigApp();
    }),
    provideBrowserGlobalErrorListeners(),
    provideZonelessChangeDetection(),
    provideRouter(routes, withPreloading(FlagBasedPreloadingStrategy)),
    provideHttpClient(
      withInterceptors([
        uploadInterceptor,
        requestInterceptor,
        errorInterceptor,
        jwtInterceptor,
      ]),
      withFetch(),
    ),
    provideNzIcons(),
    provideNzI18n(it_IT),
    importProvidersFrom(NzModalModule),
    provideAnimationsAsync(),
    provideTranslateService({
      loader: {
        provide: TranslateLoader,
        useFactory: TranslateLoaderFn,
        deps: [HttpClient],
      },
    }),
  ],
};

export function TranslateLoaderFn(http: HttpClient) {
  return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}
