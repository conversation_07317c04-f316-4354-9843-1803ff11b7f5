import {
  Component,
  Input,
  On<PERSON><PERSON>roy,
  OnInit,
  forwardRef,
  inject,
} from '@angular/core';
import {
  ControlValueAccessor,
  FormsModule,
  NG_VALUE_ACCESSOR,
} from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { Editor, NgxEditorModule, Toolbar } from 'ngx-editor';

@Component({
  selector: 'app-translated-editor',
  standalone: true,
  imports: [NgxEditorModule, FormsModule],
  template: `
    <ngx-editor-menu [editor]="editor" [toolbar]="toolbar"></ngx-editor-menu>
    <ngx-editor
      [editor]="editor"
      [placeholder]="placeholder"
      (ngModelChange)="onContentChange($event)"
      [ngModel]="value"
    >
    </ngx-editor>
  `,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => TranslatedEditorComponent),
      multi: true,
    },
  ],
})
export class TranslatedEditorComponent
  implements OnInit, OnDestroy, ControlValueAccessor
{
  @Input() toolbar: Toolbar = [
    ['bold', 'italic'],
    ['underline'],
    ['blockquote'],
    ['ordered_list', 'bullet_list'],
    [{ heading: ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'] }],
    ['link'],
    ['align_left', 'align_center', 'align_right'],
  ];

  @Input() placeholder: string = '';

  editor!: Editor;
  value: any = '';

  private translateService = inject(TranslateService);
  private observer?: MutationObserver;

  // ControlValueAccessor
  private onChange = (value: any) => {};
  private onTouched = () => {};

  ngOnInit(): void {
    this.editor = new Editor();
    this.setupTranslations();
  }

  ngOnDestroy(): void {
    this.editor?.destroy();
    this.observer?.disconnect();
  }

  private setupTranslations(): void {
    // Aspetta che l'editor sia renderizzato
    setTimeout(() => {
      this.translateStaticElements();
      this.observeForDynamicElements();
    }, 100);
  }

  private translateStaticElements(): void {
    // Traduce il dropdown "Heading"
    const headingDropdown = document.querySelector(
      '.NgxEditor__MenuItem--Dropdown',
    );
    if (headingDropdown && headingDropdown.textContent?.trim() === 'Heading') {
      headingDropdown.textContent =
        this.translateService.instant('EDITOR.heading');
    }

    // Traduce i tooltip dei pulsanti
    this.translateTooltips();
  }

  private translateTooltips(): void {
    const tooltipMappings: { [key: string]: string } = {
      Bold: 'EDITOR.bold',
      Italic: 'EDITOR.italic',
      Underline: 'EDITOR.underline',
      Strike: 'EDITOR.strike',
      Blockquote: 'EDITOR.blockquote',
      'Ordered List': 'EDITOR.orderedList',
      'Bullet List': 'EDITOR.bulletList',
      Link: 'EDITOR.link',
      'Align Left': 'EDITOR.alignLeft',
      'Align Center': 'EDITOR.alignCenter',
      'Align Right': 'EDITOR.alignRight',
    };

    Object.entries(tooltipMappings).forEach(
      ([originalText, translationKey]) => {
        const elements = document.querySelectorAll(`[title="${originalText}"]`);
        elements.forEach((element) => {
          element.setAttribute(
            'title',
            this.translateService.instant(translationKey),
          );
        });
      },
    );
  }

  private observeForDynamicElements(): void {
    this.observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element;

            // Controlla se è stato aggiunto un dialog del link
            if (
              element.classList?.contains('NgxEditor__LinkDialog') ||
              element.querySelector('.NgxEditor__LinkDialog')
            ) {
              setTimeout(() => this.translateLinkDialog(element), 50);
            }

            // Controlla se è stato aggiunto un dropdown
            if (
              element.classList?.contains('NgxEditor__Dropdown') ||
              element.querySelector('.NgxEditor__Dropdown')
            ) {
              setTimeout(() => this.translateDropdown(element), 50);
            }
          }
        });
      });
    });

    this.observer.observe(document.body, {
      childList: true,
      subtree: true,
    });
  }

  private translateLinkDialog(container: Element): void {
    const dialog = container.classList?.contains('NgxEditor__LinkDialog')
      ? container
      : container.querySelector('.NgxEditor__LinkDialog');

    if (!dialog) return;

    // Traduce i placeholder
    const urlInput = dialog.querySelector(
      'input[placeholder*="URL"]',
    ) as HTMLInputElement;
    if (urlInput) {
      urlInput.placeholder = this.translateService.instant('EDITOR.url');
    }

    const textInput = dialog.querySelector(
      'input[placeholder*="Text"]',
    ) as HTMLInputElement;
    if (textInput) {
      textInput.placeholder = this.translateService.instant('EDITOR.text');
    }

    // Traduce il checkbox label
    const checkboxLabel = dialog.querySelector('label');
    if (checkboxLabel && checkboxLabel.textContent?.includes('new tab')) {
      checkboxLabel.textContent = this.translateService.instant(
        'EDITOR.openInNewTab',
      );
    }

    // Traduce il pulsante
    const button = dialog.querySelector('button');
    if (button && button.textContent?.includes('Insert')) {
      button.textContent = this.translateService.instant('EDITOR.insert');
    }
  }

  private translateDropdown(container: Element): void {
    const dropdown = container.classList?.contains('NgxEditor__Dropdown')
      ? container
      : container.querySelector('.NgxEditor__Dropdown');

    if (!dropdown) return;

    // Traduce "Heading" nel dropdown
    const headingItem = dropdown.querySelector('.NgxEditor__DropdownItem');
    if (headingItem && headingItem.textContent?.trim() === 'Heading') {
      headingItem.textContent = this.translateService.instant('EDITOR.heading');
    }
  }

  onContentChange(content: any): void {
    this.value = content;
    this.onChange(content);
    this.onTouched();
  }

  // ControlValueAccessor implementation
  writeValue(value: any): void {
    this.value = value || '';
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    // Implementa se necessario
  }
}
