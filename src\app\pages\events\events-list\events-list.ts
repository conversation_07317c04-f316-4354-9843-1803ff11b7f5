import { Component, inject } from '@angular/core';
import { HeaderService } from '@core/services/utils/header';
import { currentSectionType } from '@models/enums/current-section';

@Component({
  selector: 'app-events-list',
  imports: [],
  templateUrl: './events-list.html',
  styleUrl: './events-list.less',
})
export class EventsList {
  private headerService = inject(HeaderService);

  constructor() {
    this.headerService.setCurrentSection(currentSectionType.eventList);
  }
}
