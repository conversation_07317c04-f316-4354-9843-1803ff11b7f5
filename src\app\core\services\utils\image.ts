import {
  HttpClient,
  HttpErrorResponse,
  HttpRequest,
  HttpResponse,
} from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { log } from '@core/utils/logger';
import { FileResponse, IImageSize } from '@models/interfaces/file-upload';
import { NzUploadXHRArgs } from 'ng-zorro-antd/upload';
import { BehaviorSubject, Observable, tap } from 'rxjs';
import { MessageService } from './message';

/**
 * Servizio per la gestione delle immagini e upload di file.
 * Fornisce metodi per caricare immagini e gestire errori di upload.
 */
@Injectable({
  providedIn: 'root',
})
export class ImageService {
  private http = inject(HttpClient);
  private messageService = inject(MessageService);

  public uploadOnError$ = new BehaviorSubject<any>(null);

  uploadFileLocal(
    item: NzUploadXHRArgs,
    imageLimits: IImageSize,
  ): Observable<NzUploadXHRArgs> {
    const formData = new FormData();
    formData.append('file', <any>item.file);
    formData.append('imageLimits', JSON.stringify(<any>imageLimits));

    const req = new HttpRequest('POST', item?.action, formData, {
      reportProgress: true,
      withCredentials: false,
    });

    return new Observable((observer) => {
      this.http
        .request(req)
        .pipe(
          tap((event: HttpResponse<FileResponse>) => {
            observer.next(item);
            observer.complete();
          }),
        )
        .subscribe({
          next: (event: HttpResponse<FileResponse>) => {
            item.onSuccess(event.body, item.file, event);
            // this.messageService.addSuccessMessage('imageValid');
          },
          error: (err: HttpErrorResponse) => {
            log('ITEM ON ERROR: ', err);
            this.uploadOnError$.next(item);
            this.messageService.addErrorMessage(err.error.limits);
            item.onError('File bigger than 400 x 508 resolution', item.file);
          },
        });
    });
  }
}
