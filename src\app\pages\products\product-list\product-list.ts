import {
  ChangeDetectorRef,
  Component,
  inject,
  OnDestroy,
  signal,
  TemplateRef,
  viewChild,
} from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Router } from '@angular/router';
import { EllipsisDirective } from '@core/directives/ellipsis';
import { DateFormatPipe } from '@core/pipes/date.pipe';
import { NullValuePipe } from '@core/pipes/null-value.pipe';
import { CategoriesService } from '@core/services/http/categories';
import { ProductsService } from '@core/services/http/products';
import { BreadcrumbService } from '@core/services/utils/breadcrumb';
import { HeaderService } from '@core/services/utils/header';
import { MessageService } from '@core/services/utils/message';
import { ModalService } from '@core/services/utils/modal';
import { log } from '@core/utils/logger';
import { currentSectionType } from '@models/enums/current-section';
import { IImage } from '@models/interfaces/image';
import { IProduct } from '@models/interfaces/product';
import { TranslateModule } from '@ngx-translate/core';
import { TableComponent } from '@shared/table/table';
import { TableQueryService } from '@shared/table/table-query.service';
import { ITableRowAction } from '@shared/table/types/table.action';
import {
  ITableColumn,
  requestFilterOperatorType,
} from '@shared/table/types/table.column';
import { ITableQuery } from '@shared/table/types/table.query';
import { IRefreshData } from '@shared/table/types/table.refresh-data';
import { ITableSetting } from '@shared/table/types/table.settings';
import { TagBooleanStatusComponent } from '@shared/tags/tag-boolean-status/tag-boolean-status';
import {
  NzAvatarComponent,
  NzAvatarGroupComponent,
} from 'ng-zorro-antd/avatar';
import { NzImageModule, NzImageService } from 'ng-zorro-antd/image';
import { NzTooltipDirective } from 'ng-zorro-antd/tooltip';
import { NzTypographyModule } from 'ng-zorro-antd/typography';
import { map } from 'rxjs';

@Component({
  selector: 'app-product-list',
  standalone: true,
  imports: [
    NzAvatarComponent,
    NzAvatarGroupComponent,
    NzTooltipDirective,
    TranslateModule,
    NzImageModule,
    NullValuePipe,
    DateFormatPipe,
    EllipsisDirective,
    TagBooleanStatusComponent,
    TableComponent,
    NzTypographyModule,
  ],
  providers: [TableQueryService],
  templateUrl: './product-list.html',
  styleUrl: './product-list.less',
})
export class ProductListComponent implements OnDestroy {
  private cdr = inject(ChangeDetectorRef);

  // SERVICES
  private productService = inject(ProductsService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private modalService = inject(ModalService);
  private headerService = inject(HeaderService);
  private messageService = inject(MessageService);
  private imageService = inject(NzImageService);
  private breadcrumbService = inject(BreadcrumbService);
  private categoryService = inject(CategoriesService);

  readonly tplId = viewChild<TemplateRef<any>>('tplId');
  readonly tplIsOnline = viewChild<TemplateRef<any>>('tplIsOnline');
  readonly tplName = viewChild<TemplateRef<any>>('tplName');
  readonly tplImage = viewChild<TemplateRef<any>>('tplImage');
  readonly tplCategory = viewChild<TemplateRef<any>>('tplCategory');
  readonly loading = signal<boolean>(false);
  readonly data = signal<any[]>([]);
  readonly dataQuery = signal<any>(null);
  readonly tableLayoutSettings = signal<ITableSetting>(<ITableSetting>{});
  readonly tableQueryRequest = signal<ITableQuery | null>(null);
  readonly refreshData = signal<IRefreshData>({
    interval: 0,
    min: 0,
    max: 180,
    step: 30,
  });

  routeData = toSignal(this.route.data, { initialValue: { isChild: false } });
  parentId = toSignal(
    this.route.parent?.paramMap.pipe(map((params) => params.get('id'))),
    {
      initialValue: null,
    },
  );

  constructor() {
    this.tableLayoutSettings.set({
      singleRowActions: this.buildRowActions(),
      listOfColumns: this.buildTableColumns(),
      dynamicTableHeightOffset:
        this.routeData()?.isChild && this.parentId() ? 425 : 375,
      pagination: {
        total: 0,
        pageSize: 50,
        pageIndex: 1,
      },
    });

    // Initialize with empty data to ensure table renders properly
    this.data.set([]);
    this.headerService.setCurrentSection(currentSectionType.productList);

    if (this.routeData()?.isChild && this.parentId()) {
      const breadcrumbData = this.categoryService.category$()?.name;
      if (!breadcrumbData) {
        this.categoryService.readOne(this.parentId()).subscribe({
          next: (res) => {
            this.breadcrumbService.setBreadcrumbData(res.data!.name);
            this.breadcrumbService.setIsLoading(false);
          },
        });
      } else {
        this.breadcrumbService.setBreadcrumbData(breadcrumbData);
        this.breadcrumbService.setIsLoading(false);
      }
    }
  }

  /**
   * Cleanup del componente alla distruzione.
   * Resetta il breadcrumb service allo stato iniziale.
   * @returns void
   */
  ngOnDestroy(): void {
    this.breadcrumbService.reset();
  }

  onQueryChange(query: ITableQuery) {
    this.tableQueryRequest.set(query);
    this.loading.set(true);
    if (this.parentId() && this.routeData()?.isChild) {
      const filter = [
        {
          operator: requestFilterOperatorType.arrayContains,
          items: [
            {
              name: 'categories',
              operator: requestFilterOperatorType.arrayContains,
              type: 'array',
              value: this.parentId(),
            },
          ],
        },
      ];
      query.filter = [...query.filter, ...filter];
    }
    this.productService.search(query.meta, query.filter).subscribe((value) => {
      this.data.set(value.data);
      const currentSettings = this.tableLayoutSettings();
      currentSettings.pagination.total = value.meta!.page.total;
      currentSettings.pagination.pageIndex = value.meta!.page.pageIndex;
      currentSettings.pagination.pageSize = value.meta!.page.pageSize;
      this.tableLayoutSettings.set({ ...currentSettings });
      this.loading.set(false);
      this.cdr.detectChanges();
    });
    this.cdr.detectChanges();
  }

  ngAfterViewInit(): void {
    this.tableLayoutSettings().listOfColumns.forEach((col) => {
      if (col.cellTemplateName) {
        const templateRef = this[col.cellTemplateName];
        if (templateRef && templateRef()) {
          col.cellTemplate = templateRef();
        }
      }
    });
  }

  private buildRowActions(): ITableRowAction[] {
    return [
      {
        label: 'ACTIONS.modify',
        icon: 'edit',
        callbackFn: (row: IProduct) => {
          if (this.routeData()?.isChild && this.parentId()) {
            this.router.navigate(['/products', row.id]);
          } else {
            this.router.navigate([row.id], { relativeTo: this.route });
          }
        },
      },
      {
        label: 'ACTIONS.delete',
        icon: 'delete',
        callbackFn: (row: IProduct) => {
          this.modalService.confirmDelete({
            confirmFn: () => {
              this.messageService.addLoadingMessage();
              this.productService.delete(row.id).subscribe({
                next: () => {
                  this.onQueryChange(this.tableQueryRequest()!);
                  this.messageService.addSuccessMessage(
                    'PRODUCTS.deleteSuccess',
                  );
                  log(`PRODUCT ID: ${row.id} - Eliminato`);
                },
              });
            },
            title: 'PRODUCTS.confirmDeleteTitle',
            subtitle: 'PRODUCTS.confirmDeleteSubtitle',
          });
        },
        danger: true,
      },
    ];
  }

  private buildTableColumns(): ITableColumn[] {
    return [
      {
        id: 'id',
        cellTemplate: null,
        cellTemplateName: 'tplId',
        title: 'ID',
        sortOrder: null,
        sortFn: true,
        width: '60px',
        lockOnLeft: true,
        visible: true,
      },
      {
        id: 'images',
        title: 'PRODUCTS.images',
        cellTemplate: null,
        cellTemplateName: 'tplImage',
        width: '50px',
        visible: true,
      },
      {
        id: 'name',
        title: 'PRODUCTS.name',
        cellTemplate: null,
        cellTemplateName: 'tplName',
        sortOrder: null,
        sortFn: true,
        width: '80px',
        visible: true,
        hasSearchFilter: true,
      },
      {
        id: 'categories',
        title: 'PRODUCTS.category',
        cellTemplate: null,
        cellTemplateName: 'tplCategory',
        sortOrder: null,
        sortFn: true,
        width: '80px',
        visible: true,
      },
      {
        id: 'isOnline',
        title: 'PRODUCTS.isOnline',
        cellTemplate: null,
        cellTemplateName: 'tplIsOnline',
        sortOrder: null,
        sortFn: true,
        width: '50px',
        visible: true,
        filterFn: true,
        listOfFilter: [
          {
            text: 'PRODUCTS.no',
            value: false,
          },
          {
            text: 'PRODUCTS.yes',
            value: true,
          },
        ],
      },
    ];
  }

  goToDetail(productId: string) {
    if (this.routeData()?.isChild && this.parentId()) {
      this.router.navigate(['/products', productId]);
    } else {
      this.router.navigate([productId], { relativeTo: this.route });
    }
  }

  onImageClick(images: IImage[]): void {
    const imagePreview = images.map((image) => {
      return { src: image.url, alt: image.name };
    });
    this.imageService.preview(imagePreview, { nzZoom: 1, nzRotate: 0 });
  }
}
