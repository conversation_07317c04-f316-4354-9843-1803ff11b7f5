import { Component, input, OnChanges } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { NzIconDirective } from 'ng-zorro-antd/icon';
import { NzTagComponent } from 'ng-zorro-antd/tag';

@Component({
  selector: 'app-tag-boolean-status',
  standalone: true,
  imports: [NzTagComponent, NzIconDirective, TranslateModule],
  templateUrl: './tag-boolean-status.html',
  styleUrl: './tag-boolean-status.less',
})
export class TagBooleanStatusComponent implements OnChanges {
  readonly value = input<boolean>(undefined);
  readonly trueText = input<string>(undefined);
  readonly falseText = input<string>(undefined);
  protected text: string;
  protected icon: string;
  protected color: string;

  ngOnChanges(): void {
    this.setTag();
  }

  setTag() {
    switch (true) {
      case !!this.value():
        this.text = this.trueText();
        this.icon = 'check-circle';
        this.color = 'success';
        break;
      default:
        this.text = this.falseText();
        this.icon = 'close-circle';
        this.color = 'error';
        break;
    }
  }
}
