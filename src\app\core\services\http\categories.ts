import { HttpClient } from '@angular/common/http';
import { inject, Injectable, signal } from '@angular/core';
import { Router } from '@angular/router';
import { environment } from '@env/environment';
import {
  IBaseResponse,
  IFindResponseMeta,
} from '@models/interfaces/base-response';
import { ICategory } from '@models/interfaces/category';
import { CrudApiOperations } from '@models/interfaces/crud-api';
import { IRequestFilter, IRequestMeta } from '@shared/table/types/table.query';
import { Observable, tap } from 'rxjs';

/**
 * Servizio per la gestione delle categorie di prodotto.
 * Fornisce CRUD e stato locale per le categorie tramite API.
 */
@Injectable({
  providedIn: 'root',
})
export class CategoriesService implements CrudApiOperations<ICategory, string> {
  // SERVICES
  private http = inject(HttpClient);
  private router = inject(Router);

  // VARIABLES
  private _baseCategoriesApi = `${environment.api.categories}`;

  private _category = signal<ICategory | undefined>(undefined);
  readonly category$ = this._category.asReadonly();

  private _categories = signal<ICategory[] | undefined>(undefined);
  readonly categories$ = this._categories.asReadonly();

  private _categoryId = signal<string | undefined>(undefined);
  readonly categoryId$ = this._categoryId.asReadonly();

  /**
   * Imposta l'ID della categoria attiva nello stato locale.
   * @param categoryId ID della categoria da impostare come attiva
   * @returns void
   */
  public setCategoryId(categoryId: string): void {
    this._categoryId.set(categoryId);
  }

  /**
   * Imposta la categoria attiva nello stato locale.
   * @param category Categoria da impostare come attiva
   * @returns void
   */
  public setCategory(category: ICategory | undefined): void {
    this._category.set(category);
  }

  /**
   * Imposta la lista delle categorie nello stato locale.
   * @param categories Array di categorie da impostare
   * @returns void
   */
  public setCategories(categories: ICategory[]): void {
    this._categories.set(categories);
  }

  /**
   * Crea una nuova categoria tramite API.
   * @param category Oggetto categoria da creare
   * @returns Observable con la risposta della creazione
   */
  create(category: ICategory) {
    return this.http.post<IBaseResponse<ICategory>>(
      `${this._baseCategoriesApi}`,
      category,
    );
  }

  /**
   * Aggiorna una categoria esistente tramite API.
   * @param categoryId ID della categoria da aggiornare
   * @param category Nuovi dati della categoria
   * @returns Observable con la risposta dell'aggiornamento
   */
  update(categoryId: string, category: ICategory) {
    return this.http.put<IBaseResponse<ICategory>>(
      `${this._baseCategoriesApi}/${categoryId}`,
      category,
    );
  }

  /**
   * Elimina una categoria tramite API.
   * @param categoryId ID della categoria da eliminare
   * @returns Observable con la risposta dell'eliminazione
   */
  delete(categoryId: string) {
    return this.http.delete<void>(`${this._baseCategoriesApi}/${categoryId}`);
  }

  /**
   * Recupera una singola categoria tramite API e aggiorna lo stato locale.
   * @param categoryId ID della categoria da recuperare
   * @returns Observable con la categoria trovata
   */
  readOne(categoryId: string) {
    return this.http
      .get<IBaseResponse<ICategory>>(`${this._baseCategoriesApi}/${categoryId}`)
      .pipe(
        tap((value) => {
          this.setCategory(value.data!);
        }),
      );
  }

  /**
   * Recupera tutte le categorie tramite API e aggiorna lo stato locale.
   * @returns Observable con la lista delle categorie
   */
  readAll() {
    return this.http
      .get<IBaseResponse<ICategory[]>>(`${this._baseCategoriesApi}`)
      .pipe(
        tap((value) => {
          this.setCategories(value.data!);
        }),
      );
  }

  /**
   * Esegue una ricerca avanzata sulle categorie tramite API.
   * @param meta Metadati di paginazione/ordinamento
   * @param filter Filtri di ricerca
   * @returns Observable con la lista delle categorie e metadati
   */
  search(
    meta: IRequestMeta,
    filter: IRequestFilter[],
  ): Observable<IBaseResponse<ICategory[], IFindResponseMeta>> {
    return this.http.post<IBaseResponse<ICategory[], IFindResponseMeta>>(
      `${this._baseCategoriesApi}/search`,
      {
        meta,
        filter,
      },
    );
  }
}
