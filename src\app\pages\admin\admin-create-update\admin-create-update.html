<!-- MAIN CONTAINER -->
<div class="container">
  <!-- LOADING SPINNER -->
  <nz-spin [nzSpinning]="loading()">
    <!-- ADMIN FORM -->
    <form nz-form [formGroup]="baseForm" nzLayout="vertical">
      <!-- PERSONAL INFO SECTION -->
      <div nz-row>
        <!-- NAME INPUT -->
        <div nz-col nzSpan="8" class="w-100 pr-8">
          <app-input-generic
            [parentForm]="baseForm"
            [controlName]="'name'"
            [label]="'ADMIN.name'"
            [placeholder]="'INPUTS.namePlaceholder'"
            [minLength]="2"
            [maxLength]="50"
          ></app-input-generic>
        </div>
        <!-- SURNAME INPUT -->
        <div nz-col nzSpan="8" class="w-100">
          <app-input-generic
            [parentForm]="baseForm"
            [controlName]="'surname'"
            [label]="'ADMIN.surname'"
            [placeholder]="'INPUTS.surnamePlaceholder'"
            [minLength]="2"
            [maxLength]="50"
          ></app-input-generic>
        </div>
      </div>

      <!-- CONTACT INFO & STATUS SECTION -->
      <div nz-row>
        <!-- EMAIL INPUT -->
        <div nz-col nzSpan="8" class="w-100 pr-8">
          <app-input-generic
            [parentForm]="baseForm"
            [controlName]="'email'"
            [label]="'ADMIN.email'"
            [placeholder]="'INPUTS.emailPlaceholder'"
            [minLength]="5"
            [maxLength]="50"
          ></app-input-generic>
        </div>
        <!-- STATUS TAGS (UPDATE MODE ONLY) -->
        @if (crudMode() == crudActionType.update) {
          <div nz-col nzSpan="8" class="w-100">
            <div nz-row class="w-100 tags" style="justify-content: flex-end">
              <div style="margin-right: 1.3rem">
                <app-tag-active-status
                  [value]="baseForm.get('isActive').value"
                ></app-tag-active-status>
              </div>
              <div>
                <app-tag-enable-status
                  [value]="baseForm.get('isEnabled').value"
                ></app-tag-enable-status>
              </div>
            </div>
          </div>
        }
      </div>
    </form>
  </nz-spin>
</div>

<!-- ACTION BUTTONS TEMPLATE -->
<ng-template #tplButton>
  <!-- SAVE/UPDATE BUTTON -->
  <button
    nz-button
    nzType="primary"
    (click)="onDataSaveClick()"
    [disabled]="!baseForm.valid || loading()"
  >
    {{ saveButtonTitle() | translate }}
    @if (crudMode() === crudActionType.create) {
      <i nz-icon nzType="client-ui:plus"></i>
    } @else {
      <i nz-icon nzType="save"></i>
    }
  </button>
</ng-template>
