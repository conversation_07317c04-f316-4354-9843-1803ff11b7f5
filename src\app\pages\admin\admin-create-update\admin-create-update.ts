import {
  Component,
  DestroyRef,
  Signal,
  TemplateRef,
  afterNextRender,
  effect,
  inject,
  signal,
  viewChild,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { trackEvent } from '@aptabase/web';
import { AdminService } from '@core/services/http/admin';
import { AuthService } from '@core/services/http/auth';
import { BreadcrumbService } from '@core/services/utils/breadcrumb';
import { HeaderService } from '@core/services/utils/header';
import { MessageService } from '@core/services/utils/message';
import { ModalService } from '@core/services/utils/modal';
import { FormUtils } from '@core/utils/form';
import { CustomValidators } from '@core/validators/custom.validator';
import { CreateUpdateItem } from '@models/entities/create-update-item';
import { crudActionType } from '@models/enums/crud-action-type';
import { currentSectionType } from '@models/enums/current-section';
import { IAdmin } from '@models/interfaces/admin';
import { TranslateModule } from '@ngx-translate/core';
import { InputGenericComponent } from '@shared/inputs/input-generic/input-generic';
import { TagActiveStatusComponent } from '@shared/tags/tag-active-status/tag-active-status';
import { TagEnableStatusComponent } from '@shared/tags/tag-enable-status/tag-enable-status';
import { NzButtonComponent } from 'ng-zorro-antd/button';
import { NzFormDirective } from 'ng-zorro-antd/form';
import { NzColDirective, NzRowDirective } from 'ng-zorro-antd/grid';
import { NzIconDirective } from 'ng-zorro-antd/icon';
import { NzSpinComponent } from 'ng-zorro-antd/spin';
import { switchMap, tap } from 'rxjs';
@Component({
  selector: 'app-admin-create-update',
  standalone: true,
  imports: [
    InputGenericComponent,
    FormsModule,
    NzFormDirective,
    TranslateModule,
    ReactiveFormsModule,
    NzRowDirective,
    NzColDirective,
    TagEnableStatusComponent,
    TagActiveStatusComponent,
    NzSpinComponent,
    NzButtonComponent,
    NzIconDirective,
  ],
  templateUrl: './admin-create-update.html',
  styleUrl: './admin-create-update.less',
})
export class AdminreateUpdate extends CreateUpdateItem {
  // SERVICES
  private fb = inject(FormBuilder);
  private adminService = inject(AdminService);
  private messageService = inject(MessageService);
  private route = inject(ActivatedRoute);
  private destroyRef = inject(DestroyRef);
  private router = inject(Router);
  private authService = inject(AuthService);
  private headerService = inject(HeaderService);
  private breadcrumbService = inject(BreadcrumbService);
  private modalService = inject(ModalService);

  // PROPERTIES
  protected baseForm: FormGroup;
  protected crudMode = signal<crudActionType>(crudActionType.create);
  protected crudActionType = crudActionType;
  protected loading = signal<boolean>(true);
  protected adminId = signal<string>('');
  protected saveButtonTitle = signal<string>('ADMIN.saveAdmin');
  protected abortButtonTitle = signal<string>('ADMIN.deleteAdmin');
  protected admin: Signal<IAdmin> = this.adminService.admin$;
  protected isAdmin = this.authService.isAdmin;

  private adminEffect = effect(() => {
    if (this.admin()) {
      FormUtils.fillUpdateDataForm(this.baseForm, this.admin());
    }
  });

  // VIEWCHILD
  protected tplButton = viewChild<TemplateRef<any>>('tplButton');

  /**
   * Costruttore del componente admin create/update.
   * Inizializza il form, gestisce i parametri di routing e configura l'header.
   */
  constructor() {
    super();
    trackEvent('admin_create_update_page');
    this.adminId.set(this.route.snapshot.paramMap.get('id'));

    this.initForm();
    this.route.paramMap
      .pipe(
        takeUntilDestroyed(),
        switchMap((params) => {
          return this.route.data.pipe(
            tap((data) => {
              this.crudMode.set(<crudActionType>data['crudType']);
              this.setCrudMode();
            }),
          );
        }),
      )
      .subscribe();

    afterNextRender(() => {
      this.headerService.setCurrentSection(
        currentSectionType.adminDetail,
        this.tplButton(),
      );
    });
  }

  /**
   * Imposta la modalità CRUD (create o update) in base ai dati di routing.
   * @returns void
   */
  override setCrudMode() {
    switch (this.crudMode()) {
      case crudActionType.create:
        this.setCreateMode();
        break;
      case crudActionType.update:
        this.setUpdateMode();
        break;
    }
  }

  /**
   * Configura il componente per la modalità di creazione di un nuovo admin.
   * Resetta il form, imposta i titoli dei pulsanti e mostra il breadcrumb.
   * @returns void
   */
  override setCreateMode() {
    this.baseForm.reset();
    this.saveButtonTitle.set('ADMIN.saveAdmin');
    this.abortButtonTitle.set('abort');
    this.breadcrumbService.setIsLoading(false);
    this.loading.set(false);
  }

  /**
   * Configura il componente per la modalità di aggiornamento di un admin esistente.
   * Carica i dati dell'admin tramite HTTP, popola il form e configura il breadcrumb.
   * @returns void
   */
  override setUpdateMode() {
    this.baseForm.get('email').disable();
    this.saveButtonTitle.set('ADMIN.updateAdmin');

    this.adminService
      .readOne(this.adminId())
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: (res) => {
          FormUtils.fillUpdateDataForm(this.baseForm, res.data);
          const breadcrumbData = res.data?.name + ' ' + res.data?.surname;
          this.breadcrumbService.setBreadcrumbData(breadcrumbData);
          this.breadcrumbService.setIsLoading(false);
          this.loading.set(false);
        },
        error: () => {
          this.router.navigateByUrl('/admin');
        },
      });
  }

  /**
   * Inizializza il form reattivo con i campi e le validazioni per l'admin.
   * @returns void
   */
  private initForm() {
    this.baseForm = this.fb.group({
      name: [
        '',
        [
          Validators.required,
          Validators.minLength(2),
          Validators.maxLength(50),
        ],
      ],
      surname: [
        '',
        [
          Validators.required,
          Validators.minLength(2),
          Validators.maxLength(50),
        ],
      ],
      email: [
        '',
        [
          Validators.required,
          Validators.minLength(5),
          Validators.maxLength(50),
          Validators.pattern(CustomValidators.emailRegex),
        ],
      ],
      isEnabled: [{ value: true, disabled: true }, [Validators.required]],
      isActive: [{ value: true, disabled: true }, [Validators.required]],
    });
  }

  /**
   * Gestisce la creazione di un nuovo admin.
   * Invia i dati del form al server tramite HTTP POST.
   * @returns void
   */
  onDataSubmit() {
    this.loading.set(true);
    const admin = this.baseForm.value;
    this.messageService.addLoadingMessage('loading');
    this.adminService
      .create(admin)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: () => {
          this.baseForm.reset();
          this.messageService.addSuccessMessage('ADMIN.createSuccess');

          this.loading.set(false);
        },
        error: () => {
          this.loading.set(false);
        },
      });
  }

  /**
   * Gestisce l'aggiornamento di un admin esistente.
   * Invia i dati modificati al server tramite HTTP PUT e aggiorna il breadcrumb.
   * @returns void
   */
  onDataUpdate() {
    this.loading.set(true);
    const admin = this.baseForm.value;
    this.messageService.addLoadingMessage('loading');

    this.adminService
      .updateCustomer(this.adminId(), admin)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: (res) => {
          this.messageService.addSuccessMessage('ADMIN.updateSuccess');

          const breadcrumbData = res.data?.name + ' ' + res.data?.surname;
          this.breadcrumbService.setBreadcrumbData(breadcrumbData);
          this.breadcrumbService.setIsLoading(false);
          this.loading.set(false);
        },
        error: () => {
          this.loading.set(false);
        },
      });
  }

  /**
   * Gestisce il click del pulsante di salvataggio.
   * Determina se eseguire creazione o aggiornamento in base alla modalità CRUD.
   * @returns void
   */
  onDataSaveClick() {
    switch (this.crudMode()) {
      case crudActionType.create:
        this.onDataSubmit();
        break;
      case crudActionType.update:
        this.onDataUpdate();
        break;
    }
  }

  /**
   * Gestisce il click del pulsante di annullamento/eliminazione.
   * In modalità create: naviga alla lista admin.
   * In modalità update: mostra modal di conferma per eliminazione.
   * @returns void
   */
  onAbortClick() {
    // this.loading.set(true);
    switch (this.crudMode()) {
      case crudActionType.create:
        this.router.navigateByUrl('/admin');
        break;

      case crudActionType.update:
        this.modalService.confirmDelete({
          confirmFn: () => {
            this.messageService.addLoadingMessage();
            this.adminService.delete(this.adminId()).subscribe({
              next: () => {
                this.loading.set(false);
                this.router.navigateByUrl('/admin');
                this.messageService.addSuccessMessage('ADMIN.deleteSuccess');
              },
            });
          },
          title: 'ADMIN.confirmDeleteTitle',
          subtitle: 'ADMIN.confirmDeleteSubtitle',
        });
        break;
    }
  }

  /**
   * Cleanup del componente alla distruzione.
   * Resetta il breadcrumb service allo stato iniziale resetta l'admin.
   * @returns void
   */
  ngOnDestroy(): void {
    this.breadcrumbService.reset();
    this.adminService.setAdmin(null);
  }
}
