@import (multiple)
  "../../../node_modules/ng-zorro-antd/src/style/themes/default";
@import (multiple) "./base";

//Main
@primary-color: @deep-red-wine;
@secondary-color: @cyan;
@accent-color: @blue-marin;
@border-radius-base: 6px;
@input-affix-margin: 6px;
@text-color-primary: @black;
@text-color-secondary: @strong_grey;

//Header
@header-background: @white;
@header-top-border: @light_grey;

//Footer
@footer-text-opacity: rgba(0, 0, 0, 0.548);
@footer-text-light-opacity: rgba(0, 0, 0, 0.705);

//List
@list-header-background: @white;
@list-footer-background: @white;

//Page Header
@top-bar-actions-hover: @hover-color;
@circle-radius: #f0f2f5;
@triangle-radius: @component-bg;
@avatar-bg: @primary-color;
//
@component-bg: @extra_light_grey;
@dropdown-menu-bg: @white;
@sider-menu: @white;

//Layout
@layout-sider-background: @white;
@layout-header-background: @white;
@layout-main-page: @white;
@menu-inline-submenu-bg: @white;

@box-inset: inset 0 1.5rem 3.75rem rgba(0, 0, 0, 0.1);

// Typography //
@typography-title-margin-top: 0;
@typography-title-margin-bottom: 0;

//Spin
@spin-bg: #fdfdfd;

//Scrollbar
@scrollbar-color: inherit;

// Sidebar
@info-box-bg: @light_grey;

// Text editor
@text-editor-border-color: rgba(0, 0, 0, 0.2);
