<app-table
  [tableLayoutSettings]="tableLayoutSettings()"
  [loading]="loading()"
  [data]="data()"
  [refresh]="refreshData()"
  (onRefreshTimerTick)="onQueryChange(tableQueryRequest()!)"
  [tableId]="'_categoriesTable'"
  (onQueryChange)="onQueryChange($event)"
>
</app-table>

<ng-template #tplId let-element="element" let-row="row">
  <a
    ellipsis
    nz-typography
    [nzEllipsis]="element"
    nz-tooltip
    [nzTooltipTitle]="element | translate"
    (click)="goToDetail(element)"
    (keyup.enter)="goToDetail(element)"
    tabindex="0"
    >{{ element }}
  </a>
</ng-template>

<ng-template #tplImage let-element="element" let-row="row">
  <nz-avatar-group class="d-inline pointer">
    @for (el of element; track $index) {
      <nz-avatar
        nz-tooltip
        [nzTooltipTitle]="el.name"
        [nzSrc]="el.url"
        [nzSize]="'small'"
        (click)="onImageClick(element)"
      ></nz-avatar>
    }
  </nz-avatar-group>
</ng-template>

<ng-template #tplNullValue>
  {{ null | nullValue }}
</ng-template>

<ng-template #tplDate let-element="element">
  {{ element | dateFormat: "onlyDate" | nullValue }}
</ng-template>

<ng-template #tplStatus let-element="element">
  <app-tag-news-status [value]="element"></app-tag-news-status>
</ng-template>

<ng-template #tplPinned let-element="element">
  @if (element) {
    <nz-tag nzColor="#55acee">
      <nz-icon nzType="client-ui:pinned" />
    </nz-tag>
  }
</ng-template>

<ng-template #tplText let-element="element">
  <p
    ellipsis
    nz-typography
    [nzEllipsis]="element"
    nz-tooltip
    [nzTooltipTitle]="element"
  >
    {{ element }}
  </p>
</ng-template>

<ng-template #tplTags let-element="element">
  <span
    ellipsis
    nz-typography
    [nzEllipsis]="getFullTextTag(element)"
    nz-tooltip
    [nzTooltipTitle]="getFullTextTag(element)"
  >
    {{ getFullTextTag(element) }}
  </span>
</ng-template>
