import { Routes } from '@angular/router';
import { authGuard } from '@core/guards/auth-guard';
import { roleType } from '@models/enums/role';
import { ErrorComponent } from '@pages/error/error.component';

export const routes: Routes = [
  {
    path: '',
    redirectTo: 'products',
    pathMatch: 'full',
  },
  // {
  //   path: 'dashboard',
  //   component: Dashboard,
  //   canActivate: [authGuard],
  //   data: { preload: true, rolePermission: [roleType.admin, roleType.staff] },
  // },
  {
    path: 'auth',
    loadChildren: () =>
      import('@pages/auth/auth.routes').then((m) => m.AUTH_ROUTES),
  },
  {
    path: 'products',
    loadChildren: () =>
      import('./pages/products/products.routes').then((m) => m.PRODUCTS_ROUTES),
    data: { rolePermission: [roleType.admin, roleType.staff] },
    canActivate: [authGuard],
  },
  {
    path: 'categories',
    loadChildren: () =>
      import('./pages/categories/categories.routes').then(
        (m) => m.CATEGORIES_ROUTES,
      ),
    data: { rolePermission: [roleType.admin, roleType.staff] },
    canActivate: [authGuard],
  },
  {
    path: 'awards',
    loadChildren: () =>
      import('./pages/awards/awards.routes').then((m) => m.AWARDS_ROUTES),
    data: { rolePermission: [roleType.admin, roleType.staff] },
    canActivate: [authGuard],
  },
  {
    path: 'admin',
    loadChildren: () =>
      import('./pages/admin/admin.routes').then((m) => m.ADMIN_ROUTES),
    data: { rolePermission: [roleType.admin] },
    canActivate: [authGuard],
  },
  {
    path: 'staff',
    loadChildren: () =>
      import('./pages/staff/staff.routes').then((m) => m.STAFF_ROUTES),
    data: { rolePermission: [roleType.admin, roleType.staff] },
    canActivate: [authGuard],
  },
  {
    path: 'news',
    loadChildren: () =>
      import('./pages/news/news.routes').then((m) => m.NEWS_ROUTES),
    data: { rolePermission: [roleType.admin, roleType.staff] },
    canActivate: [authGuard],
  },
  {
    path: 'events',
    loadChildren: () =>
      import('./pages/events/events.routes').then((m) => m.EVENTS_ROUTES),
    data: { rolePermission: [roleType.admin, roleType.staff] },
    canActivate: [authGuard],
  },
  {
    path: 'tags',
    loadChildren: () =>
      import('./pages/tags/tags.routes').then((m) => m.TAGS_ROUTES),
    data: { rolePermission: [roleType.admin] },
    canActivate: [authGuard],
  },
  { path: 'error', component: ErrorComponent },
  { path: '**', component: ErrorComponent },
];
