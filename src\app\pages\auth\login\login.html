<div class="container">
  <nz-spin [nzSpinning]="isLoading()">
    <h1 class="m-0">{{ "LOGIN.login" | translate }}</h1>
    <p class="subtitle">{{ "LOGIN.subtitle" | translate }}</p>
    <form
      nz-form
      [nzLayout]="'vertical'"
      [formGroup]="loginForm"
      class="login-form"
    >
      <app-input-generic
        [controlName]="'email'"
        [parentForm]="loginForm"
        [placeholder]="'<EMAIL>'"
        [label]="'LOGIN.email' | translate"
        [type]="'email'"
        [prefixIcon]="'user'"
        [minLength]="5"
        [maxLength]="50"
      ></app-input-generic>

      <app-input-password
        [controlName]="'password'"
        [parentForm]="loginForm"
        [placeholder]="'********'"
        [prefixIcon]="'lock'"
        [label]="'LOGIN.password' | translate"
        [minLength]="9"
        [maxLength]="50"
      ></app-input-password>

      <div nz-row class="login-form-margin">
        <div nz-col [nzSpan]="12">
          <!--        <label nz-checkbox [formControlName]="'remember'">{{-->
          <!--          'LOGIN.rememberMe' | translate-->
          <!--        }}</label>-->
        </div>
        <div class="login-form-forgot" nz-col [nzSpan]="12">
          <a
            (click)="onForgotPasswordClick()"
            (keyup)="onForgotPasswordClick()"
            tabindex="0"
            >{{ "LOGIN.forgotPassword" | translate }}</a
          >
        </div>
      </div>
    </form>

    <app-simple-button
      [type]="'primary'"
      [disabled]="!loginForm.valid || isLoading()"
      (onButtonClick)="onLoginClick()"
      [title]="'LOGIN.login' | translate | uppercase"
      [style]="{ width: '100%' }"
      [autoMinify]="false"
      data-cy="login-submit-button"
    ></app-simple-button>

    @if (loginError()) {
      <div class="error-message">
        <span nz-typography nzType="danger">{{
          "LOGIN.loginError" | translate
        }}</span>
      </div>
    }
  </nz-spin>
</div>
