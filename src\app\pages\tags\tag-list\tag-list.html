<app-table
  [tableLayoutSettings]="tableLayoutSettings()"
  [loading]="loading()"
  [data]="data()"
  [refresh]="refreshData()"
  (onRefreshTimerTick)="onQueryChange(tableQueryRequest()!)"
  [tableId]="'_categoriesTable'"
  (onQueryChange)="onQueryChange($event)"
>
</app-table>

<ng-template #tplId let-element="element" let-row="row">
  <a
    ellipsis
    nz-typography
    [nzEllipsis]="element"
    nz-tooltip
    [nzTooltipTitle]="element | translate"
    (click)="goToDetail(element)"
    >{{ element }}
  </a>
</ng-template>

<ng-template #tplName let-element="element" let-row="row">
  {{ element }}
</ng-template>

<ng-template #tplCount let-element="element">
  {{ element }}
</ng-template>

<ng-template #tplNullValue>
  {{ null | nullValue }}
</ng-template>
