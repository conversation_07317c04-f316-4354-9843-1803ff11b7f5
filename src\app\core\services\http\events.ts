import { HttpClient } from '@angular/common/http';
import { inject, Injectable, signal } from '@angular/core';
import { Router } from '@angular/router';
import { environment } from '@env/environment';
import {
  IBaseResponse,
  IFindResponseMeta,
} from '@models/interfaces/base-response';
import { CrudApiOperations } from '@models/interfaces/crud-api';
import { IEvent } from '@models/interfaces/event';
import { IRequestFilter, IRequestMeta } from '@shared/table/types/table.query';
import { Observable } from 'rxjs';

/**
 * Servizio per la gestione degli eventi.
 * Fornisce CRUD e stato locale per gli eventi tramite API.
 */
@Injectable({
  providedIn: 'root',
})
export class EventsService implements CrudApiOperations<IEvent, string> {
  // SERVICES
  private http = inject(HttpClient);
  private router = inject(Router);

  // VARIABLES
  private _baseEventsApi = `${environment.api.events}`;

  private _event = signal<IEvent | undefined>(undefined);
  readonly event$ = this._event.asReadonly();

  private _eventList = signal<IEvent[] | undefined>(undefined);
  readonly eventList$ = this._eventList.asReadonly();

  private _eventId = signal<string | undefined>(undefined);
  readonly eventId$ = this._eventId.asReadonly();

  /**
   * Imposta l'evento attivo nello stato locale.
   * @param event Evento da impostare come attivo
   * @returns void
   */
  public setEvent(event: IEvent | undefined): void {
    this._event.set(event);
  }

  /**
   * Imposta la lista degli eventi nello stato locale.
   * @param eventList Lista degli eventi da impostare
   * @returns void
   */
  public setEventList(eventList: IEvent[]): void {
    this._eventList.set(eventList);
  }

  /**
   * Imposta l'ID dell'evento attivo nello stato locale.
   * @param eventId ID dell'evento da impostare
   * @returns void
   */
  public setEventId(eventId: string | undefined): void {
    this._eventId.set(eventId);
  }

  /**
   * CRUD API Methods
   */
  create(event: IEvent): Observable<IBaseResponse<IEvent>> {
    return this.http.post<IBaseResponse<IEvent>>(this._baseEventsApi, event);
  }

  readOne(id: string): Observable<IBaseResponse<IEvent>> {
    return this.http.get<IBaseResponse<IEvent>>(`${this._baseEventsApi}/${id}`);
  }

  update(id: string, event: IEvent): Observable<IBaseResponse<IEvent>> {
    return this.http.put<IBaseResponse<IEvent>>(
      `${this._baseEventsApi}/${id}`,
      event,
    );
  }

  delete(id: string): Observable<IBaseResponse<IEvent>> {
    return this.http.delete<IBaseResponse<IEvent>>(
      `${this._baseEventsApi}/${id}`,
    );
  }

  search(
    meta: IRequestMeta,
    filter: IRequestFilter[],
  ): Observable<IBaseResponse<IEvent[], IFindResponseMeta>> {
    return this.http.post<IBaseResponse<IEvent[], IFindResponseMeta>>(
      `${this._baseEventsApi}/search`,
      {
        meta,
        filter,
      },
    );
  }
}
