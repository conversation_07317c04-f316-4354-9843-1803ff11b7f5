import {
  HttpErrorResponse,
  HttpEvent,
  HttpHandlerFn,
  HttpRequest,
  HttpResponse,
} from '@angular/common/http';
import {
  calculateBase64ImageSize,
  extractBase64FromDataUrl,
  getBase64,
  getImageObject,
} from '@app/core/utils/image';
import { IImageSize } from '@models/interfaces/file-upload';
import { Observable, Subscriber, forkJoin, of } from 'rxjs';
import { first, map, switchMap } from 'rxjs/operators';

export const uploadInterceptor = (
  req: HttpRequest<unknown>,
  next: HttpHandlerFn,
): Observable<HttpEvent<unknown>> => {
  if (req.url == 'http://localhost:4201/api/fakeImage') {
    return new Observable<any>((observer) => {
      return imageUpload(req, observer);
    });
  }
  if (req.url == 'http://localhost:4201/api/fakeVideo') {
    return new Observable<any>((observer) => {
      return videoUpload(req, observer);
    });
  }
  return next(req);
};

export const imageUpload = (
  req: HttpRequest<any>,
  observer: Subscriber<any>,
) => {
  const imageSize = JSON.parse(req.body.get('imageLimits'));
  const imageRequest$ = getBase64(req.body.get('file')).pipe(
    switchMap((base64: string) => getImageObject(base64)),
  );

  return forkJoin([imageRequest$, of(imageSize)])
    .pipe(
      first(),
      map((res) => {
        return { imageRes: res[0], imageSize: <IImageSize>res[1] };
      }),
    )
    .subscribe(
      (res: {
        imageRes: { width: number; height: number; base64: string };
        imageSize: IImageSize;
      }) => {
        const base64Image = extractBase64FromDataUrl(res.imageRes.base64);
        const sizeImage = calculateBase64ImageSize(base64Image!);
        if (sizeImage!.megabytes! <= res.imageSize.size.megabytes!) {
          const responseOK: HttpResponse<{
            body: { ok: true };
            status: number;
            statusText: string;
            url: string;
          }> = new HttpResponse<any>({
            body: { ok: true, base64: res.imageRes.base64 },
            status: 200,
            statusText: 'OK',
            url: 'http://localhost/api/fakeImage',
          });
          observer.next(responseOK);
          observer.complete();
        } else {
          const responseKO: HttpErrorResponse = new HttpErrorResponse({
            error: {
              limits: `Image doesn't respect the limits ${res.imageSize.size.megabytes!.toFixed(
                2,
              )} MB. Image size is ${sizeImage.megabytes!.toFixed(2)} MB.`,
            },
            status: 500,
            statusText: 'KO',
            url: 'http://localhost/api/fakeImage',
          });
          observer.error(responseKO);
          observer.complete();
        }
      },
    );
};

export const videoUpload = (
  req: HttpRequest<any>,
  observer: Subscriber<any>,
) => {
  const videoRequest$ = of(req.body);

  console.log('REQ BODY', req.body);

  // Convert video file to blob if it's a File object
  const convertToBlob = (file: any): Blob => {
    if (file instanceof File) {
      return new Blob([file], { type: file.type });
    }
    return file;
  };

  // Simulate upload progress
  const simulateProgress = (file: any) => {
    const totalSize = file.size || 1024 * 1024; // Default 1MB if size not available
    let uploaded = 0;
    const progressInterval = setInterval(() => {
      uploaded += Math.random() * (totalSize * 0.1); // Random progress increment

      if (uploaded >= totalSize) {
        uploaded = totalSize;
        clearInterval(progressInterval);

        // Complete the upload
        const responseOK: HttpResponse<{
          body: { ok: true; data: any; blob: Blob };
          status: number;
          statusText: string;
          url: string;
        }> = new HttpResponse<any>({
          body: {
            ok: true,
            data: file,
            blob: convertToBlob(file),
          },
          status: 200,
          statusText: 'OK',
          url: 'http://localhost/api/fakeVideo',
        });
        observer.next(responseOK);
        observer.complete();
      } else {
        // Report progress
        const progressEvent = {
          type: 1, // HttpEventType.UploadProgress
          loaded: uploaded,
          total: totalSize,
        };
        observer.next(progressEvent);
      }
    }, 100); // Update progress every 100ms
  };

  return forkJoin([videoRequest$])
    .pipe(
      first(),
      map((res) => ({ file: res[0] })),
    )
    .subscribe((res: { file: any }) => {
      console.log('RES', res);

      // Start progress simulation
      simulateProgress(res.file);
    });
};
