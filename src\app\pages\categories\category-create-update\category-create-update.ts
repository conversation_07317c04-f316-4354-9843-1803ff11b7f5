import {
  afterNextRender,
  Component,
  DestroyRef,
  inject,
  Inject,
  Optional,
  output,
  Signal,
  signal,
  TemplateRef,
  viewChild,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { CategoriesService } from '@core/services/http/categories';
import { BreadcrumbService } from '@core/services/utils/breadcrumb';
import { HeaderService } from '@core/services/utils/header';
import { ImageService } from '@core/services/utils/image';
import { MessageService } from '@core/services/utils/message';
import { ModalService } from '@core/services/utils/modal';
import { UploadService, ValidateImages } from '@core/services/utils/upload';
import { FormUtils } from '@core/utils/form';
import { log } from '@core/utils/logger';
import { CreateUpdateItem } from '@models/entities/create-update-item';
import { crudActionType } from '@models/enums/crud-action-type';
import { currentSectionType } from '@models/enums/current-section';
import { ICategory } from '@models/interfaces/category';
import { IImage } from '@models/interfaces/image';
import { TranslateModule } from '@ngx-translate/core';
import { InputGenericComponent } from '@shared/inputs/input-generic/input-generic';
import { InputImageUploadComponent } from '@shared/inputs/input-image-upload/input-image-upload.component';
import { InputTextareaComponent } from '@shared/inputs/input-textarea/input-textarea';
import { NzButtonComponent } from 'ng-zorro-antd/button';
import { NzFormDirective } from 'ng-zorro-antd/form';
import { NzColDirective, NzRowDirective } from 'ng-zorro-antd/grid';
import { NzIconDirective } from 'ng-zorro-antd/icon';
import { NZ_MODAL_DATA } from 'ng-zorro-antd/modal';
import { NzSpinComponent } from 'ng-zorro-antd/spin';
import { NzUploadFile } from 'ng-zorro-antd/upload';
import { switchMap, tap } from 'rxjs';

enum ValidatorsField {
  IMAGES = 'images',
}

@Component({
  selector: 'app-category-create-update',
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    NzFormDirective,
    NzButtonComponent,
    TranslateModule,
    InputGenericComponent,
    NzSpinComponent,
    NzRowDirective,
    NzColDirective,
    NzIconDirective,
    InputTextareaComponent,
    InputImageUploadComponent,
  ],
  providers: [UploadService],
  templateUrl: './category-create-update.html',
  styleUrl: './category-create-update.less',
})
export class CategoryCreateUpdateComponent extends CreateUpdateItem {
  // SERVICES
  private route = inject(ActivatedRoute);
  private destroyRef = inject(DestroyRef);
  private uploadService = inject(UploadService);
  private imageService = inject(ImageService);
  private categoryService = inject(CategoriesService);
  private router = inject(Router);
  private messageService = inject(MessageService);
  private modalService = inject(ModalService);
  private headerService = inject(HeaderService);
  private fb = inject(FormBuilder);
  private breadcrumbService = inject(BreadcrumbService);

  protected baseForm!: FormGroup;
  // Convert to signals
  protected saveButtonTitle = signal<string>('CATEGORIES.saveCategory');
  protected abortButtonTitle = signal<string>('CATEGORIES.deleteCategory');
  public crudMode = signal<crudActionType>(crudActionType.create);
  protected crudActionType = crudActionType;
  protected isValidForm = signal<boolean>(false);
  protected categoryId = signal<string>('');
  protected loading = signal<boolean>(true);
  protected category: Signal<ICategory | undefined> =
    this.categoryService.category$;

  /**
   * Inizializza il servizio di upload con le immagini esistenti della categoria.
   * Popola FileList e ImageList con i dati dal form.
   * @returns void
   */
  private initUploadService(): void {
    this.uploadService.FileList = FormUtils.populateImages(
      this.baseForm,
      ValidatorsField.IMAGES,
    );
    this.uploadService.ImageList = FormUtils.populateField(
      this.baseForm,
      ValidatorsField.IMAGES,
    );
  }

  protected previewImage: string | undefined = '';
  protected previewVisible = false;

  // VIEWCHILD
  public tplButton = viewChild<TemplateRef<any>>('tplButton');

  // OUTPUT
  componentReady = output<TemplateRef<any>>();

  /**
   * Inizializza il form, gestisce i parametri di routing e configura l'header.
   * Se il componente è aperto in un modal, il crudMode viene impostato dai dati del modal.
   * @returns void
   */
  constructor(@Optional() @Inject(NZ_MODAL_DATA) private modalData: any) {
    super();
    this.initForm();
    console.log(this.modalData);
    // Se siamo in un modal, impostiamo il crudMode dai dati del modal
    if (this.modalData?.crudMode) {
      console.log(this.modalData);
      this.crudMode.set(this.modalData.crudMode);
      this.setCrudMode();
    } else {
      // Comportamento normale per la pagina standalone
      this.headerService.setCurrentSection(currentSectionType.categoryDetail);
      console.log(this.route);
      console.log(this.categoryService.categoryId$());
      this.route.parent?.paramMap
        .pipe(
          takeUntilDestroyed(),
          switchMap((params) => {
            console.log(params);
            this.categoryId.set(<string>params.get('id'));
            console.log(this.categoryId());
            return this.route.data.pipe(
              tap((data) => {
                this.crudMode.set(<crudActionType>data['crudType']);
                this.setCrudMode();
              }),
            );
          }),
        )
        .subscribe();
    }

    afterNextRender(() => {
      if (!this.modalData) {
        this.headerService.setCurrentSection(
          currentSectionType.categoryDetail,
          this.tplButton(),
        );
      }
      if (this.modalData && this.tplButton()) {
        this.componentReady.emit(this.tplButton());
      }
    });
  }

  public get FileList(): NzUploadFile[] {
    return this.uploadService.FileList;
  }

  public set FileList(list: NzUploadFile[]) {
    this.uploadService.FileList = list;
  }

  public get ImageList(): IImage[] {
    return this.uploadService.ImageList;
  }

  /**
   * Inizializza il form reattivo con i campi e le validazioni per la categoria.
   * Include validazioni per nome, visibilità in home e immagini.
   * @returns void
   */
  private initForm() {
    this.baseForm = this.fb.group({
      id: ['', [Validators.nullValidator]],
      name: ['', [Validators.required, Validators.minLength(3)]],
      description: ['', [Validators.required, Validators.minLength(10)]],
      images: ['', [Validators.required, ValidateImages(1, 5)]],
    });
  }

  /**
   * Imposta la modalità CRUD (create o update) in base ai dati di routing.
   * @returns void
   */
  override setCrudMode() {
    switch (this.crudMode()) {
      case crudActionType.create:
        this.setCreateMode();
        break;
      case crudActionType.update:
        this.setUpdateMode();
        break;
    }
  }

  /**
   * Configura il componente per la modalità di creazione di una nuova categoria.
   * Resetta il form, imposta i titoli dei pulsanti e mostra il breadcrumb.
   * @returns void
   */
  override setCreateMode() {
    this.baseForm.reset();
    this.categoryService.setCategory(undefined);
    this.saveButtonTitle.set('CATEGORIES.createCategory');
    this.abortButtonTitle.set('abort');
    this.breadcrumbService.setIsLoading(false);
    this.loading.set(false);
  }

  /**
   * Configura il componente per la modalità di aggiornamento di una categoria esistente.
   * Carica i dati della categoria tramite HTTP, popola il form e configura il breadcrumb.
   * @returns void
   */
  override setUpdateMode() {
    this.saveButtonTitle.set('CATEGORIES.updateCategory');
    this.abortButtonTitle.set('CATEGORIES.deleteCategory');

    this.categoryService.readOne(this.categoryId()).subscribe({
      next: (res) => {
        FormUtils.fillUpdateDataForm(this.baseForm, res.data!);
        this.initUploadService();
        const breadcrumbData = res.data?.name;
        this.breadcrumbService.setBreadcrumbData(breadcrumbData);
        this.breadcrumbService.setIsLoading(false);
        this.loading.set(false);
      },
      error: () => {
        this.router.navigateByUrl('/categories');
      },
    });
  }

  /**
   * Hook di inizializzazione del componente.
   * Avvia l'osservazione dei cambiamenti del form.
   * @returns void
   */
  ngOnInit(): void {
    this.observeFormChanges();
  }

  /**
   * Osserva i cambiamenti del form e gestisce gli errori di upload delle immagini.
   * Imposta isValidForm in base alla validità del form e agli errori di upload.
   * @returns void
   */
  observeFormChanges() {
    this.imageService.uploadOnError$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((item) => {
        this.isValidForm.set(false);
      });

    this.baseForm.valueChanges
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        this.isValidForm.set(this.baseForm.valid);
      });
  }

  /**
   * Gestisce il click del pulsante di annullamento/eliminazione.
   * In modalità create: naviga alla lista categorie.
   * In modalità update: mostra modal di conferma per eliminazione.
   * @returns void
   */
  onAbortClick() {
    // this.loading.set(true);
    switch (this.crudMode()) {
      case crudActionType.create:
        this.router.navigateByUrl('/categories');
        break;

      case crudActionType.update:
        this.modalService.confirmDelete({
          confirmFn: () => {
            this.messageService.addLoadingMessage();
            this.categoryService.delete(this.categoryId()).subscribe({
              next: () => {
                this.loading.set(false);
                this.router.navigateByUrl('/categories');
                this.messageService.addSuccessMessage(
                  'CATEGORIES.deleteSuccess',
                );
                log(`CATEGORY ID: ${this.categoryId()} - Eliminato`);
              },
            });
          },
          title: 'CATEGORIES.confirmDeleteTitle',
          subtitle: 'CATEGORIES.confirmDeleteSubtitle',
        });
        break;
    }
  }

  /**
   * Gestisce il click del pulsante di salvataggio.
   * Determina se eseguire creazione o aggiornamento in base alla modalità CRUD.
   * @returns void
   */
  onDataSaveClick() {
    switch (this.crudMode()) {
      case crudActionType.create:
        this.onDataSubmit();
        break;
      case crudActionType.update:
        this.onDataUpdate();
        break;
    }
  }

  /**
   * Gestisce la creazione di una nuova categoria.
   * Invia i dati del form al server tramite HTTP POST.
   * @returns void
   */
  override onDataSubmit() {
    this.loading.set(true);
    const category = this.baseForm.value;
    FormUtils.removeObjectNullProperties(category);
    this.messageService.addLoadingMessage('loading');
    this.categoryService.create(category).subscribe({
      next: (res) => {
        this.messageService.addSuccessMessage('CATEGORIES.createSuccess');
        this.loading.set(false);
        if (this.modalData) {
          this.modalService.closeModal();
        } else {
          this.router.navigate(['categories', res.data!.id]);
        }
      },
      error: () => {
        this.loading.set(false);
      },
    });
  }

  /**
   * Gestisce l'aggiornamento di una categoria esistente.
   * Invia i dati modificati al server tramite HTTP PUT e aggiorna il breadcrumb.
   * @returns void
   */
  override onDataUpdate() {
    this.loading.set(true);
    const category = this.baseForm.value;
    this.messageService.addLoadingMessage('loading');

    this.categoryService.update(this.categoryId(), category).subscribe({
      next: (res) => {
        this.messageService.addSuccessMessage('CATEGORIES.updateSuccess');
        const breadcrumbData = res.data?.name;
        this.breadcrumbService.setBreadcrumbData(breadcrumbData);
        this.breadcrumbService.setIsLoading(false);
        this.loading.set(false);
      },
      error: () => {
        this.loading.set(false);
      },
    });
  }

  /**
   * Cleanup del componente alla distruzione.
   * Resetta il breadcrumb service allo stato iniziale.
   * @returns void
   */
  ngOnDestroy(): void {
    this.breadcrumbService.reset();
  }
}
