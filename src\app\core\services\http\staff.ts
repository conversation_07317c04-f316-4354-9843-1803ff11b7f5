import { HttpClient } from '@angular/common/http';
import { inject, Injectable, signal } from '@angular/core';
import { environment } from '@env/environment';
import {
  IBaseResponse,
  IFindResponseMeta,
} from '@models/interfaces/base-response';
import { IStaff } from '@models/interfaces/staff';
import { IRequestFilter, IRequestMeta } from '@shared/table/types/table.query';
import { Observable, tap } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class StaffService {
  // SERVICES
  private http = inject(HttpClient);

  // PROPERTIES

  private _baseStaffApi = `${environment.api.staff}`;

  private _staff = signal<IStaff | undefined>(undefined);
  readonly staff$ = this._staff.asReadonly();

  private _staffs = signal<IStaff[] | undefined>(undefined);
  readonly staffs$ = this._staffs.asReadonly();

  /**
   * Imposta lo staff attivo nello stato locale.
   * @param staff Staff da impostare come attiva
   * @returns void
   */
  public setStaff(staff: IStaff | undefined): void {
    this._staff.set(staff);
  }

  /**
   * Recupera un singolo staff tramite API e aggiorna lo stato locale.
   * @param staffId ID dello staff da recuperare
   * @returns Observable con la categoria trovata
   */
  readOne(staffId: string) {
    return this.http
      .get<IBaseResponse<IStaff>>(`${this._baseStaffApi}/${staffId}`)
      .pipe(
        tap((value) => {
          this.setStaff(value.data!);
        }),
      );
  }

  /**
   * Elimina una categoria tramite API.
   * @param staffId ID della categoria da eliminare
   * @returns Observable con la risposta dell'eliminazione
   */
  delete(staffId: string) {
    return this.http.delete<void>(`${this._baseStaffApi}/${staffId}`);
  }

  /**
   * Recupera tutte lo staff tramite API e aggiorna lo stato locale.
   * @returns Observable con la lista dello staff
   */
  readAll() {
    return this.http.get<IBaseResponse<IStaff[]>>(`${this._baseStaffApi}`).pipe(
      tap((value) => {
        this.setStaffs(value.data!);
      }),
    );
  }

  /**
   * Crea una nuova categoria tramite API.
   * @param staff Oggetto categoria da creare
   * @returns Observable con la risposta della creazione
   */
  create(staff: IStaff) {
    return this.http.post<IBaseResponse<IStaff>>(
      `${this._baseStaffApi}`,
      staff,
    );
  }

  /**
   * Aggiorna una categoria esistente tramite API.
   * @param staffId ID della categoria da aggiornare
   * @param staff Nuovi dati della categoria
   * @returns Observable con la risposta dell'aggiornamento
   */
  update(staffId: string, staff: IStaff) {
    return this.http.put<IBaseResponse<IStaff>>(
      `${this._baseStaffApi}/${staffId}`,
      staff,
    );
  }

  /**
   * Imposta la lista dello staff nello stato locale.
   * @param staffs Array di staff da impostare
   * @returns void
   */
  public setStaffs(staffs: IStaff[]): void {
    this._staffs.set(staffs);
  }

  search(
    meta: IRequestMeta,
    filter: IRequestFilter[],
  ): Observable<IBaseResponse<IStaff[], IFindResponseMeta>> {
    return this.http.post<IBaseResponse<IStaff[], IFindResponseMeta>>(
      `${this._baseStaffApi}/search`,
      {
        meta,
        filter,
      },
    );
  }
}
