import { HttpClient } from '@angular/common/http';
import { inject, Injectable, signal } from '@angular/core';
import { Router } from '@angular/router';
import { environment } from '@env/environment';
import {
  IBaseResponse,
  IFindResponseMeta,
} from '@models/interfaces/base-response';
import { CrudApiOperations } from '@models/interfaces/crud-api';
import { INews } from '@models/interfaces/news';
import { IRequestFilter, IRequestMeta } from '@shared/table/types/table.query';
import { Observable, tap } from 'rxjs';

/**
 * Servizio per la gestione delle news.
 * Fornisce CRUD e stato locale per le news tramite API.
 */
@Injectable({
  providedIn: 'root',
})
export class NewsService implements CrudApiOperations<INews, string> {
  // SERVICES
  private http = inject(HttpClient);
  private router = inject(Router);

  // VARIABLES
  private _baseNewsApi = `${environment.api.news}`;

  private _news = signal<INews | undefined>(undefined);
  readonly news$ = this._news.asReadonly();

  private _newsList = signal<INews[] | undefined>(undefined);
  readonly newsList$ = this._newsList.asReadonly();

  private _newsId = signal<string | undefined>(undefined);
  readonly newsId$ = this._newsId.asReadonly();

  /**
   * Imposta la news attiva nello stato locale.
   * @param news News da impostare come attiva
   * @returns void
   */
  public setNews(news: INews | undefined): void {
    this._news.set(news);
  }

  /**
   * Imposta la lista delle news nello stato locale.
   * @param newsList Lista delle news da impostare
   * @returns void
   */
  public setNewsList(newsList: INews[]): void {
    this._newsList.set(newsList);
  }

  /**
   * Imposta l'ID della news attiva nello stato locale.
   * @param newsId ID della news da impostare
   * @returns void
   */
  public setNewsId(newsId: string): void {
    this._newsId.set(newsId);
  }

  /**
   * Crea una nuova news tramite API.
   * @param news Oggetto news da creare
   * @returns Observable con la risposta della creazione
   */
  create(news: INews) {
    return this.http.post<IBaseResponse<INews>>(`${this._baseNewsApi}`, news);
  }

  /**
   * Aggiorna una news esistente tramite API.
   * @param newsId ID della news da aggiornare
   * @param news Nuovi dati della news
   * @returns Observable con la risposta dell'aggiornamento
   */
  update(newsId: string, news: INews) {
    return this.http.put<IBaseResponse<INews>>(
      `${this._baseNewsApi}/${newsId}`,
      news,
    );
  }

  /**
   * Elimina una news tramite API.
   * @param newsId ID della news da eliminare
   * @returns Observable con la risposta dell'eliminazione
   */
  delete(newsId: string) {
    return this.http.delete<void>(`${this._baseNewsApi}/${newsId}`);
  }

  /**
   * Recupera una singola news tramite API e aggiorna lo stato locale.
   * @param newsId ID della news da recuperare
   * @returns Observable con la news trovata
   */
  readOne(newsId: string) {
    return this.http
      .get<IBaseResponse<INews>>(`${this._baseNewsApi}/${newsId}`)
      .pipe(
        tap((value) => {
          this.setNews(value.data!);
        }),
      );
  }

  /**
   * Esegue una ricerca avanzata sulle news tramite API.
   * @param meta Metadati di paginazione/ordinamento
   * @param filter Filtri di ricerca
   * @returns Observable con la lista delle news e metadati
   */
  search(
    meta: IRequestMeta,
    filter: IRequestFilter[],
  ): Observable<IBaseResponse<INews[], IFindResponseMeta>> {
    return this.http.post<IBaseResponse<INews[], IFindResponseMeta>>(
      `${this._baseNewsApi}/search`,
      {
        meta,
        filter,
      },
    );
  }
}
