<div class="header">
  <nz-page-header class="site-page-header" [nzSubtitle]="tplSubtitle">
    <!--avatar-->
    <nz-avatar
      nz-page-header-avatar
      nzSize="small"
      [nzIcon]="currentMenu()?.icon"
    ></nz-avatar>

    <!--title-->
    <nz-page-header-title>
      <span>
        <a
          (click)="gotoPage(currentMenu()?.routerLink)"
          (keyup)="gotoPage(currentMenu()?.routerLink)"
          tabindex="0"
          >{{ currentMenu()?.title | translate }}</a
        ></span
      >
    </nz-page-header-title>

    <!--subtitle-->
    <ng-template #tplSubtitle>
      @for (item of urls() | slice: 1; track $index) {
        @if (!breadcrumbIsLoading()) {
          <nz-page-header-subtitle>/</nz-page-header-subtitle>
          <nz-page-header-subtitle>
            <a
              class="breadcrumb"
              [ngClass]="{ disabled: $last }"
              (click)="!$last ? gotoSubpage(item) : null"
              (keyup)="!$last ? gotoSubpage(item) : null"
              tabindex="0"
              >{{
                ($index === 0 && breadcrumbData() ? breadcrumbData() : item)
                  | translate
              }}</a
            >
          </nz-page-header-subtitle>
        }
      }
    </ng-template>

    <!--extra-->
    <nz-page-header-extra>
      <span>
        @switch (currentSection()) {
          @case (currentSectionType.dashboard) {
            <app-dashboard-header></app-dashboard-header>
          }
          @case (currentSectionType.categoryList) {
            <app-category-list-header></app-category-list-header>
          }
          @case (currentSectionType.categoryDetail) {
            <app-category-detail-header></app-category-detail-header>
          }
          @case (currentSectionType.adminList) {
            <app-admin-list-header></app-admin-list-header>
          }
          @case (currentSectionType.adminDetail) {
            <app-admin-detail-header></app-admin-detail-header>
          }
          @case (currentSectionType.staffList) {
            <app-staff-list-header></app-staff-list-header>
          }
          @case (currentSectionType.staffDetail) {
            <app-staff-detail-header></app-staff-detail-header>
          }
          @case (currentSectionType.productDetail) {
            <app-product-detail-header></app-product-detail-header>
          }
          @case (currentSectionType.productList) {
            <app-product-list-header></app-product-list-header>
          }
          @case (currentSectionType.awardList) {
            <app-awards-list-header></app-awards-list-header>
          }
          @case (currentSectionType.awardDetail) {
            <app-awards-detail-header></app-awards-detail-header>
          }
          @case (currentSectionType.tagList) {
            <app-tag-list-header></app-tag-list-header>
          }
          @case (currentSectionType.tagDetail) {
            <app-tag-detail-header></app-tag-detail-header>
          }

          @case (currentSectionType.newsList) {
            <app-news-list-header></app-news-list-header>
          }
          @case (currentSectionType.newsDetail) {
            <app-news-detail-header></app-news-detail-header>
          }
          @case (currentSectionType.eventList) {
            <app-event-list-header></app-event-list-header>
          }
          @case (currentSectionType.eventDetail) {
            <app-event-detail-header></app-event-detail-header>
          }

          @default {}
        }
      </span>
    </nz-page-header-extra>
  </nz-page-header>
  <nz-divider class="divider"></nz-divider>
</div>
