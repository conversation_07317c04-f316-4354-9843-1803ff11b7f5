<!-- MAIN CONTAINER -->
<div class="container">
  <!-- STAFF FORM -->
  <form nz-form class="form" [formGroup]="baseForm" nzLayout="vertical">
    <!-- LOADING SPINNER -->
    <nz-spin [nzSpinning]="loading()">
      <!-- STAFF PERSONAL INFO SECTION -->
      <div nz-row class="w-100" [nzGutter]="16">
        <!-- STAFF NAME -->
        <div nz-col [nzSpan]="12">
          <div nz-row [nzGutter]="16">
            <div nz-col [nzSpan]="12">
              <app-input-generic
                [parentForm]="baseForm"
                [controlName]="'name'"
                [minLength]="2"
                [maxLength]="200"
                [label]="'STAFF.name' | translate"
                [placeholder]="'STAFF.namePlaceholder' | translate"
              ></app-input-generic>
            </div>
            <div nz-col [nzSpan]="12">
              <app-input-generic
                [parentForm]="baseForm"
                [controlName]="'surname'"
                [minLength]="2"
                [maxLength]="200"
                [label]="'STAFF.surname' | translate"
                [placeholder]="'STAFF.surnamePlaceholder' | translate"
              ></app-input-generic>
            </div>
          </div>
          <div nz-row [nzGutter]="16">
            <div nz-col [nzSpan]="12">
              <app-input-generic
                [parentForm]="baseForm"
                [controlName]="'email'"
                [pattern]="emailRegex"
                [label]="'STAFF.email' | translate"
                [placeholder]="'STAFF.emailPlaceholder' | translate"
              ></app-input-generic>
            </div>
            <div nz-col [nzSpan]="12">
              <app-input-generic
                [parentForm]="baseForm"
                [controlName]="'role'"
                [minLength]="2"
                [maxLength]="200"
                [label]="'STAFF.role' | translate"
                [placeholder]="'STAFF.rolePlaceholder' | translate"
              ></app-input-generic>
            </div>
          </div>
          <div nz-row [nzGutter]="16">
            <div nz-col [nzSpan]="24">
              <app-input-textarea
                [parentForm]="baseForm"
                [minLength]="2"
                [maxLength]="800"
                [controlName]="'description'"
                [label]="'STAFF.description' | translate"
                [placeholder]="'STAFF.descriptionPlaceholder' | translate"
                [size]="{ minRows: 2, maxRows: 12 }"
              ></app-input-textarea>
            </div>
          </div>
        </div>
        <div nz-col [nzSpan]="12">
          <!-- STAFF IMAGE SECTION -->
          <div nz-row [nzAlign]="'top'">
            <div nz-row class="w-100" [nzGutter]="16">
              <div nz-col [nzSpan]="12">
                <!-- STAFF IMAGE UPLOAD -->
                <app-input-image-upload
                  [parentForm]="baseForm"
                  [controlName]="'images'"
                  [label]="'image'"
                  [fileType]="'image/png,image/jpeg'"
                  [showAddButton]="FileList?.length === 0"
                  [imageContainerSize]="{ width: '200px', height: '200px' }"
                  [maxImages]="1"
                  [fileList]="FileList || []"
                  [imageList]="ImageList || []"
                ></app-input-image-upload>
              </div>
            </div>
          </div>
        </div>
      </div>
    </nz-spin>
  </form>
</div>

<!-- ACTION BUTTONS TEMPLATE -->
<ng-template #tplButton>
  <!-- SAVE/UPDATE BUTTON -->
  <button
    nz-button
    nzType="primary"
    (click)="onDataSaveClick()"
    [disabled]="!isValidForm() || loading()"
  >
    {{ saveButtonTitle() | translate }}
    @if (crudMode() === crudActionType.create) {
      <i nz-icon nzType="client-ui:plus"></i>
    } @else {
      <i nz-icon nzType="save"></i>
    }
  </button>
</ng-template>
