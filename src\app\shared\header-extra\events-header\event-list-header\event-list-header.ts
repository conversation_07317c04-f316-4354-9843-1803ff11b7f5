import { Component, inject } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { PopoverButtonComponent } from '@shared/buttons/popover-button/popover-button';
import { SimpleButtonComponent } from '@shared/buttons/simple-button/simple-button';
import { NzSpaceComponent, NzSpaceItemDirective } from 'ng-zorro-antd/space';

@Component({
  selector: 'app-event-list-header',
  imports: [
    NzSpaceComponent,
    SimpleButtonComponent,
    NzSpaceItemDirective,
    PopoverButtonComponent,
    TranslateModule,
  ],
  templateUrl: './event-list-header.html',
  styleUrl: './event-list-header.less',
})
export class EventListHeader {
  private router = inject(Router);

  onAddEventClick() {
    this.router.navigateByUrl('/events/create');
  }
}
