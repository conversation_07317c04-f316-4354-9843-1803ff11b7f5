import { NgTemplateOutlet } from '@angular/common';
import { Component, computed, DestroyRef, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Router } from '@angular/router';
import { RolePermissionDirective } from '@core/directives/role-permission';
import { AuthService } from '@core/services/http/auth';
import { StaffService } from '@core/services/http/staff';
import { HeaderService } from '@core/services/utils/header';
import { MessageService } from '@core/services/utils/message';
import { ModalService } from '@core/services/utils/modal';
import { roleType } from '@models/enums/role';
import { TranslateModule } from '@ngx-translate/core';
import { ActionsButtonComponent } from '@shared/buttons/actions-button/actions-button';
import { PopoverButtonComponent } from '@shared/buttons/popover-button/popover-button';
import { ITableRowAction } from '@shared/table/types/table.action';
import { NzSpaceModule } from 'ng-zorro-antd/space';

@Component({
  selector: 'app-staff-detail-header',
  standalone: true,
  imports: [
    NgTemplateOutlet,
    PopoverButtonComponent,
    TranslateModule,
    NzSpaceModule,
    RolePermissionDirective,
    NgTemplateOutlet,
    ActionsButtonComponent,
  ],
  templateUrl: './staff-detail-header.html',
  styleUrl: './staff-detail-header.less',
})
export class StaffDetailHeader {
  // SERVICES
  private modalService = inject(ModalService);
  private staffService = inject(StaffService);
  private messageService = inject(MessageService);
  private router = inject(Router);
  private authService = inject(AuthService);
  private headerService = inject(HeaderService);
  private destroyRef = inject(DestroyRef);

  // PROPERTIES
  private _staff = this.staffService.staff$;
  protected tplMainButton = this.headerService.template;

  // COMPUTED
  public actions = computed<ITableRowAction[]>(() => {
    return this._staff() ? this.buildRowActions() : [];
  });

  // ENUMS
  roleType = roleType;

  /**
   * Costruisce le azioni disponibili per lo staff.
   */
  private buildRowActions(): ITableRowAction[] {
    return [
      {
        label: 'STAFF.resendActivationAccount',
        icon: 'mail',
        callbackFn: () => {
          this.modalService.infoModal({
            confirmFn: () => {
              this.messageService.addLoadingMessage();
              this.authService
                .resendActivationAccount(this._staff().id)
                .pipe(takeUntilDestroyed(this.destroyRef))
                .subscribe(() => {
                  this.messageService.addSuccessMessage(
                    'STAFF.resendActivationSuccess',
                  );
                });
            },
            title: 'STAFF.activationAccountTitle',
            subtitle: 'STAFF.activationAccountSubtitle',
          });
        },
        visibilityFn: () => this._staff().isEnabled && !this._staff().isActive,
      },
      {
        label: 'ACTIONS.delete',
        icon: 'delete',
        visibilityFn: () => this._staff()?.role !== roleType.admin,
        callbackFn: () => {
          this.modalService.confirmDelete({
            confirmFn: () => {
              this.messageService.addLoadingMessage();
              this.staffService
                .delete(this._staff()?.id)
                .pipe(takeUntilDestroyed(this.destroyRef))
                .subscribe({
                  next: () => {
                    this.router.navigateByUrl('/staff');
                    this.messageService.addSuccessMessage(
                      'STAFF.deleteSuccess',
                    );
                  },
                });
            },
            title: 'STAFF.confirmDeleteTitle',
            subtitle: 'STAFF.confirmDeleteSubtitle',
          });
        },
        danger: true,
      },
    ];
  }

  ngOnDestroy(): void {
    this.staffService.setStaff(null);
  }
}
