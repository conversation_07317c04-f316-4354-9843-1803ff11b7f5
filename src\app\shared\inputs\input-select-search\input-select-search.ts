import { Component, input, output, signal, TemplateRef } from '@angular/core';
import {
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { CheckUtils } from '@core/utils/check';
import { IImage } from '@models/interfaces/image';
import { TranslateModule } from '@ngx-translate/core';
import {
  NzFormControlComponent,
  NzFormLabelComponent,
} from 'ng-zorro-antd/form';
import { NzIconDirective } from 'ng-zorro-antd/icon';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { BehaviorSubject, debounceTime, filter, tap } from 'rxjs';

@Component({
  selector: 'app-input-select-search',
  standalone: true,
  imports: [
    NzSelectModule,
    FormsModule,
    ReactiveFormsModule,
    NzFormLabelComponent,
    NzFormControlComponent,
    TranslateModule,
    NzIconDirective,
  ],
  templateUrl: './input-select-search.html',
  styleUrl: './input-select-search.less',
})
export class InputSelectSearch {
  readonly parentForm = input<FormGroup | any>(undefined);
  readonly controlName = input<string>(undefined);
  readonly label = input<string | undefined>(undefined);
  readonly labelSuffix = input<string | undefined>(undefined);
  readonly placeholder = input<string | undefined>(undefined);
  readonly prefixIcon = input<string | undefined>(undefined);
  readonly mode = input<'multiple' | 'tags' | 'default'>('default');
  readonly size = input<'large' | 'small' | 'default'>('default');
  readonly disabled = input<boolean>(false);
  readonly optionList = input<
    | {
        label: string;
        value: any;
        images: IImage[];
      }[]
    | any
  >(undefined);
  readonly configKey = input<{
    label: string;
    value: string;
  }>({
    label: 'label',
    value: 'value',
  });
  readonly maxTagCount = input<number>(undefined);
  readonly showSearch = input<boolean>(false);
  readonly serverSearch = input<boolean>(false);
  readonly allowClear = input<boolean>(false);
  readonly optionHeightPx = input<number>(32);
  readonly imageWidth = input<number>(35);
  readonly imageHeight = input<number>(35);
  readonly style = input<{
    [key: string]: any;
  }>(undefined);
  readonly isLoading = input<boolean>(false);
  readonly minLength = input<number>(0);
  readonly notFound = input<string | TemplateRef<any>>();

  readonly onSearch = output<string>();
  readonly onProductChange = output();
  readonly labelPosition = input<'left' | 'top'>('left');
  showDropdown = signal<{ [key: string]: string }>({ display: 'none' });

  readonly showImage = input<boolean>(false);

  protected searchChange$ = new BehaviorSubject('');

  ngOnInit(): void {
    this.searchChange$
      .pipe(
        debounceTime(500),
        tap((value) => {
          if (value.length < this.minLength()) {
            this.showDropdown.set({ display: 'none' });
          }
        }),
        filter((value: string) => value.length >= this.minLength()),
      )
      .subscribe((data) => {
        if (!CheckUtils.isNullUndefinedOrEmpty(data)) this.onSearch.emit(data);
        this.showDropdown.set({});
      });

    // Subscribe to form control value changes
    const control = this.parentForm()?.get(this.controlName());
    if (control) {
      control.valueChanges.subscribe((value: any) => {
        this.onProductChange.emit(value);
      });
    }
  }

  isRequired() {
    return this.parentForm()
      .get(this.controlName())
      .hasValidator(Validators.required);
  }

  onSearchClick(value: any) {
    this.searchChange$.next(value);
  }

  compareFn = (o1: any, o2: any): boolean =>
    o1 && o2
      ? o1[this.configKey().value] === o2[this.configKey().value]
      : o1 === o2;

  getCombinedStyles(): { [key: string]: any } {
    return {
      ...this.style(),
      display: this.labelPosition() === 'left' ? 'flex' : 'block',
    };
  }
}
