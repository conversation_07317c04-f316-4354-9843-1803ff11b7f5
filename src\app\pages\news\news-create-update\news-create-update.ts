import {
  Cdk<PERSON><PERSON>,
  CdkDrag<PERSON><PERSON>le,
  CdkDropList,
  moveItemInArray,
} from '@angular/cdk/drag-drop';
import {
  afterNextRender,
  ChangeDetectorRef,
  Component,
  DestroyRef,
  inject,
  output,
  Signal,
  signal,
  TemplateRef,
  viewChild,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  AbstractControl,
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NewsService } from '@core/services/http/news';
import { TagsService } from '@core/services/http/tags';
import { BreadcrumbService } from '@core/services/utils/breadcrumb';
import { HeaderService } from '@core/services/utils/header';
import { ImageService } from '@core/services/utils/image';
import { MessageService } from '@core/services/utils/message';
import { ModalService } from '@core/services/utils/modal';
import { UploadService, ValidateImages } from '@core/services/utils/upload';
import { VideoService } from '@core/services/utils/video';
import { FormUtils } from '@core/utils/form';
import { log } from '@core/utils/logger';
import { CreateUpdateItem } from '@models/entities/create-update-item';
import { articleItemType, articleStatusType } from '@models/enums/article';
import { crudActionType } from '@models/enums/crud-action-type';
import { currentSectionType } from '@models/enums/current-section';
import { IImageSize } from '@models/interfaces/file-upload';
import { IImage } from '@models/interfaces/image';
import { IArticleItem, INews, ITag } from '@models/interfaces/news';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { TagCreateUpdateComponent } from '@pages/tags/tag-create-update/tag-create-update';
import { SimpleButtonComponent } from '@shared/buttons/simple-button/simple-button';
import { TranslatedEditorComponent } from '@shared/components/translated-editor/translated-editor.component';
import { InputCheckboxComponent } from '@shared/inputs/input-checkbox/input-checkbox';
import { InputGenericComponent } from '@shared/inputs/input-generic/input-generic';
import { InputImageUploadComponent } from '@shared/inputs/input-image-upload/input-image-upload.component';
import { InputSelectComponent } from '@shared/inputs/input-select/input-select';
import { InputSingleDatepickerComponent } from '@shared/inputs/input-single-datepicker/input-single-datepicker';
import { InputTextareaComponent } from '@shared/inputs/input-textarea/input-textarea';
import { InputVideoUploadComponent } from '@shared/inputs/input-video-upload/input-video-upload.component';
import { TagNewsStatus } from '@shared/tags/tag-news-status/tag-news-status';
import { NzButtonComponent } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzDividerComponent } from 'ng-zorro-antd/divider';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzFormDirective } from 'ng-zorro-antd/form';
import { NzColDirective, NzRowDirective } from 'ng-zorro-antd/grid';
import { NzIconDirective } from 'ng-zorro-antd/icon';
import { NzSpinComponent } from 'ng-zorro-antd/spin';
import {
  NzUploadFile,
  NzUploadModule,
  NzUploadXHRArgs,
} from 'ng-zorro-antd/upload';
import { Editor, NgxEditorModule, Toolbar } from 'ngx-editor';
import { delay, Observable, of, switchMap, tap } from 'rxjs';

enum ValidatorsField {
  IMAGES = 'images',
}

const IMAGE_LIMITS: IImageSize = {
  width: {
    min: 200,
    max: 2000,
  },
  height: {
    min: 200,
    max: 2000,
  },
  size: {
    kilobytes: 16,
    megabytes: 16,
    gigabytes: 16,
  },
};

interface INewsForm {
  id: FormControl<string>;
  title: FormControl<string>;
  summary: FormControl<string>;
  images: FormControl<IImage[]>;
  url: FormControl<string>;
  status: FormControl<articleStatusType>;
  pinned: FormControl<boolean>;
  tags: FormControl<ITag[] | string[]>;
  items: FormArray<
    FormGroup<{
      type: FormControl<articleItemType>;
      order: FormControl<number>;
      content: FormControl<any>;
    }>
  >;
  orderDate: FormControl<Date>;
}

interface IItemUploadManager {
  fileList: NzUploadFile[];
  imageList: IImage[];
  uploadService: UploadService;
}

@Component({
  selector: 'app-news-create-update',
  imports: [
    ReactiveFormsModule,
    FormsModule,
    NzFormDirective,
    NzSpinComponent,
    NzRowDirective,
    NzColDirective,
    NzButtonComponent,
    NzIconDirective,
    TranslateModule,
    InputGenericComponent,
    InputTextareaComponent,
    InputSingleDatepickerComponent,
    InputCheckboxComponent,
    NzDropDownModule,
    NzDividerComponent,
    SimpleButtonComponent,
    InputSelectComponent,
    TagNewsStatus,
    InputImageUploadComponent,
    NzCardModule,
    CdkDropList,
    CdkDrag,
    CdkDragHandle,
    NgxEditorModule,
    NzUploadModule,
    InputVideoUploadComponent,
    TranslatedEditorComponent,
  ],
  providers: [UploadService],
  templateUrl: './news-create-update.html',
  styleUrl: './news-create-update.less',
})
export class NewsCreateUpdate extends CreateUpdateItem {
  // SERVICES
  private route = inject(ActivatedRoute);
  private destroyRef = inject(DestroyRef);
  private uploadService = inject(UploadService);
  private imageService = inject(ImageService);
  private videoService = inject(VideoService);
  private newsService = inject(NewsService);
  private router = inject(Router);
  private messageService = inject(MessageService);
  private modalService = inject(ModalService);
  private headerService = inject(HeaderService);
  private tagsService = inject(TagsService);
  private fb = inject(FormBuilder);
  private breadcrumbService = inject(BreadcrumbService);
  private translateService = inject(TranslateService);
  private cdr = inject(ChangeDetectorRef);

  protected baseForm!: FormGroup<INewsForm>;
  // Convert to signals
  protected saveButtonTitle = signal<string>('NEWS.saveNews');
  protected abortButtonTitle = signal<string>('NEWS.deleteNews');
  public crudMode = signal<crudActionType>(crudActionType.create);
  protected crudActionType = crudActionType;
  protected isValidForm = signal<boolean>(false);
  protected newsId = signal<string>('');
  protected loading = signal<boolean>(true);
  protected news: Signal<INews | undefined> = this.newsService.news$;

  protected tagsList$ = this.tagsService.tags$;

  // CONSTANT SIGNALS
  protected newsStatusStyle = signal<{ [key: string]: any } | undefined>(
    undefined,
  );

  protected tplFooter = viewChild<TemplateRef<any>>('tplFooter');

  // TEXT EDITOR
  editors = signal<
    {
      position: number;
      editor: Editor;
    }[]
  >([] as { position: number; editor: Editor }[]);
  toolbar = signal<Toolbar>([
    ['bold', 'italic'],
    ['underline'],
    ['blockquote'],
    ['ordered_list', 'bullet_list'],
    [{ heading: ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'] }],
    ['link'],
    ['align_left', 'align_center', 'align_right'],
  ]);

  // UPLOAD MANAGERS PER ITEM
  itemUploadManagers = signal<Map<number, IItemUploadManager>>(new Map());

  articleItemOptionList = signal<{ label: string; value: articleItemType }[]>([
    { label: 'NEWS.text', value: articleItemType.text },
    { label: 'NEWS.images', value: articleItemType.images },
    { label: 'NEWS.videos', value: articleItemType.videos },
  ]);

  // ENUMS
  articleStatusType = articleStatusType;
  articleItemType = articleItemType;

  /**
   * Crea o recupera l'upload manager per un item specifico
   */
  getItemUploadManager(index: number): IItemUploadManager {
    const managers = this.itemUploadManagers();
    if (!managers.has(index)) {
      const newManager: IItemUploadManager = {
        fileList: [],
        imageList: [],
        uploadService: new UploadService(),
      };
      managers.set(index, newManager);
      this.itemUploadManagers.set(new Map(managers));
    }
    return managers.get(index)!;
  }

  /**
   * Rimuove l'upload manager per un item specifico
   */
  removeItemUploadManager(index: number): void {
    const managers = this.itemUploadManagers();
    if (managers.has(index)) {
      managers.get(index)?.uploadService.clean();
      managers.delete(index);
      this.itemUploadManagers.set(new Map(managers));
    }
  }

  /**
   * Riordina gli upload managers quando gli items vengono riordinati
   */
  reorderItemUploadManagers(previousIndex: number, currentIndex: number): void {
    const managers = this.itemUploadManagers();
    const newManagers = new Map<number, IItemUploadManager>();

    // Ottieni il manager da spostare
    const managerToMove = managers.get(previousIndex);

    // Riordina tutti i manager
    managers.forEach((manager, index) => {
      if (index === previousIndex) {
        // Questo manager verrà spostato, non lo aggiungiamo qui
        return;
      }

      let newIndex = index;

      // Calcola la nuova posizione in base al movimento
      if (previousIndex < currentIndex) {
        // Movimento verso destra
        if (index > previousIndex && index <= currentIndex) {
          newIndex = index - 1;
        }
      } else {
        // Movimento verso sinistra
        if (index >= currentIndex && index < previousIndex) {
          newIndex = index + 1;
        }
      }

      newManagers.set(newIndex, manager);
    });

    // Aggiungi il manager spostato nella nuova posizione
    if (managerToMove) {
      newManagers.set(currentIndex, managerToMove);
    }

    this.itemUploadManagers.set(newManagers);
  }

  /**
   * Inizializza il servizio di upload con le immagini esistenti della news.
   * Popola FileList e ImageList con i dati dal form.
   * @returns void
   */
  private initUploadService(): void {
    this.uploadService.FileList = FormUtils.populateImages(
      this.baseForm,
      ValidatorsField.IMAGES,
    );
    this.uploadService.ImageList = FormUtils.populateField(
      this.baseForm,
      ValidatorsField.IMAGES,
    );
  }

  /**
   * Inizializza gli upload manager per gli items esistenti
   */
  private initItemUploadManagers(news: INews): void {
    news.items?.forEach((item, index) => {
      if (
        item.type === articleItemType.images ||
        item.type === articleItemType.videos
      ) {
        const manager = this.getItemUploadManager(index);

        // Se il content è un array di immagini, popolalo
        if (Array.isArray(item.content)) {
          const images = item.content as IImage[];
          manager.fileList = FormUtils.populateImages(
            { get: () => ({ value: images }) } as any,
            'value',
          );
          manager.imageList = images;
        }
      }
    });
  }

  protected previewImage: string | undefined = '';
  protected previewVisible = false;

  // VIEWCHILD
  public tplButton = viewChild<TemplateRef<any>>('tplButton');

  // OUTPUT
  componentReady = output<TemplateRef<any>>();

  /**
   * Costruttore del componente news create/update.
   * Inizializza il form, gestisce i parametri di routing e configura l'header.
   * @returns void
   */
  constructor() {
    super();
    this.initForm();

    // Carica i tag dal server
    this.tagsService.readAll().subscribe();

    this.route.paramMap
      .pipe(
        takeUntilDestroyed(),
        switchMap((params) => {
          console.log(params);
          this.newsId.set(<string>params.get('id'));
          console.log(this.newsId());
          return this.route.data.pipe(
            tap((data) => {
              this.crudMode.set(<crudActionType>data['crudType']);
              this.setCrudMode();
            }),
          );
        }),
      )
      .subscribe();

    afterNextRender(() => {
      this.headerService.setCurrentSection(
        currentSectionType.newsDetail,
        this.tplButton(),
      );
    });
  }

  public get FileList(): NzUploadFile[] {
    return this.uploadService.FileList;
  }

  public set FileList(list: NzUploadFile[]) {
    this.uploadService.FileList = list;
  }

  public get ImageList(): IImage[] {
    return this.uploadService.ImageList;
  }

  public get imagesField(): AbstractControl {
    return this.baseForm.get(ValidatorsField.IMAGES)!;
  }

  /**
   * Inizializza il form reattivo con i campi e le validazioni per la news.
   * Include validazioni per titolo, contenuto, data e immagini.
   * @returns void
   */
  private initForm() {
    this.baseForm = this.fb.group({
      id: this.fb.control(''),
      title: this.fb.nonNullable.control('', [
        Validators.required,
        Validators.minLength(2),
        Validators.maxLength(120),
      ]),
      summary: this.fb.nonNullable.control('', [
        Validators.required,
        Validators.minLength(2),
        Validators.maxLength(300),
      ]),
      images: this.fb.nonNullable.control(
        [],
        [Validators.required, ValidateImages(1, 1)],
      ),
      url: this.fb.nonNullable.control(''),
      status: this.fb.nonNullable.control(articleStatusType.draft),
      pinned: this.fb.nonNullable.control(false),
      tags: this.fb.nonNullable.control([], [Validators.required]),
      items: this.fb.array(
        [] as FormGroup<{
          type: FormControl<articleItemType>;
          order: FormControl<number>;
          content: FormControl<any>;
        }>[],
      ),
      orderDate: this.fb.nonNullable.control(new Date()),
    });

    this.baseForm.valueChanges.subscribe((res) =>
      console.log('FORM CHANGED', res),
    );
  }

  get items(): FormArray {
    return this.baseForm.get('items') as FormArray;
  }

  /**
   * Imposta la modalità CRUD (create o update) in base ai dati di routing.
   * @returns void
   */
  override setCrudMode() {
    switch (this.crudMode()) {
      case crudActionType.create:
        this.setCreateMode();
        break;
      case crudActionType.update:
        this.setUpdateMode();
        break;
    }
  }

  /**
   * Configura il componente per la modalità di creazione di una nuova news.
   * @returns void
   */
  override setCreateMode() {
    this.baseForm.reset();
    this.addNewItem({
      type: articleItemType.text,
      order: 0,
      content: '',
    });
    this.setItemValidators(
      articleItemType.text,
      this.items.at(0).get('content') as FormControl<any>,
    );
    this.newsService.setNews(undefined);
    this.saveButtonTitle.set('NEWS.saveNews');
    this.abortButtonTitle.set('abort');
    this.breadcrumbService.setIsLoading(false);
    this.loading.set(false);
  }

  /**
   * Configura il componente per la modalità di aggiornamento di una news esistente.
   * Carica i dati della news tramite HTTP, popola il form e configura il breadcrumb.
   * @returns void
   */
  override setUpdateMode() {
    this.saveButtonTitle.set('NEWS.updateNews');
    this.abortButtonTitle.set('NEWS.deleteNews');

    this.newsService.readOne(this.newsId()).subscribe({
      next: (res) => {
        console.log('RES.DATA', res.data);
        this.baseForm.get('id')?.patchValue(res.data!.id);
        this.baseForm.get('title')?.patchValue(res.data!.title);
        this.baseForm.get('summary')?.patchValue(res.data!.summary);
        this.baseForm.get('status')?.patchValue(res.data!.status);
        this.baseForm.get('pinned')?.patchValue(res.data!.pinned);
        this.baseForm.get('orderDate')?.patchValue(res.data!.orderDate);
        this.baseForm.get('images')?.patchValue(res.data!.images);

        const _tags = res.data!.tags.map((tag: any) => (<ITag>tag)?.id);

        this.baseForm.get('tags')?.patchValue(_tags);

        // Pulisci gli items esistenti e resetta l'editor
        this.items.clear();
        this.editors.set([]);

        res.data?.items.forEach((item) => {
          console.log('form', this.items.value);
          this.addNewItem(item);
        });

        console.log('items', this.items.value);
        console.log('upload manager', this.itemUploadManagers());

        // Inizializza il servizio di upload con le immagini esistenti della news
        this.initUploadService();
        // Inizializza gli upload manager per gli items esistenti
        this.initItemUploadManagers(res.data!);
        const breadcrumbData = res.data?.title;
        this.breadcrumbService.setBreadcrumbData(breadcrumbData);
        this.breadcrumbService.setIsLoading(false);
        this.loading.set(false);
      },
      error: () => {
        this.router.navigateByUrl('/news');
      },
    });
  }

  /**
   * Gestisce il click del pulsante di salvataggio.
   * Determina se eseguire creazione o aggiornamento in base alla modalità CRUD.
   * @returns void
   */
  onDataSaveClick() {
    if (this.crudMode() === crudActionType.create) {
      this.onDataSubmit();
    } else {
      this.onDataUpdate();
    }
  }
  /**
   * Hook di inizializzazione del componente.
   * Avvia l'osservazione dei cambiamenti del form.
   * @returns void
   */
  ngOnInit(): void {
    this.observeFormChanges();
  }

  /**
   * Osserva i cambiamenti del form e gestisce gli errori di upload delle immagini.
   * Imposta isValidForm in base alla validità del form e agli errori di upload.
   * @returns void
   */
  observeFormChanges() {
    this.imageService.uploadOnError$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        this.isValidForm.set(false);
      });

    this.baseForm.valueChanges
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        this.isValidForm.set(this.baseForm.valid);
      });
  }

  /**
   * Gestisce il click del pulsante di annullamento/eliminazione.
   * In modalità create: naviga alla lista categorie.
   * In modalità update: mostra modal di conferma per eliminazione.
   * @returns void
   */
  onAbortClick() {
    // this.loading.set(true);
    switch (this.crudMode()) {
      case crudActionType.create:
        this.router.navigateByUrl('/news');
        break;

      case crudActionType.update:
        this.modalService.confirmDelete({
          confirmFn: () => {
            this.messageService.addLoadingMessage();
            this.newsService.delete(this.newsId()).subscribe({
              next: () => {
                this.loading.set(false);
                this.router.navigateByUrl('/news');
                this.messageService.addSuccessMessage('NEWS.deleteSuccess');
                log(`NEWS ID: ${this.newsId()} - Eliminato`);
              },
            });
          },
          title: 'NEWS.confirmDeleteTitle',
          subtitle: 'NEWS.confirmDeleteSubtitle',
        });
        break;
    }
  }

  orderItems() {
    this.items.controls.forEach((item, index) => {
      item.get('order')?.patchValue(index);
    });
  }

  setItemValidators(type: articleItemType, control: FormControl<any>) {
    switch (type) {
      case articleItemType.text:
        control.setValidators([
          Validators.required,
          (ctr) => {
            if (ctr.value.length < 8) {
              return { required: true };
            }
            return null;
          },
        ]);
        break;
      case articleItemType.images:
        control.setValidators([
          Validators.required,
          (ctr: AbstractControl) => {
            if (ctr.value.length !== 1) {
              return { required: true };
            }
            return null;
          },
        ]);
        break;
      default:
        break;
    }

    control?.updateValueAndValidity();
  }

  /**
   * Gestisce la creazione di una nuova news.
   * Invia i dati del form al server tramite HTTP POST.
   * @returns void
   */
  override onDataSubmit() {
    this.loading.set(true);
    this.orderItems();
    this.prepareItemsForSubmit();
    const news = this.baseForm.getRawValue();
    FormUtils.removeObjectNullProperties(news);
    this.messageService.addLoadingMessage('loading');
    this.newsService.create(news).subscribe({
      next: (res) => {
        this.messageService.addSuccessMessage('NEWS.createSuccess');
        this.loading.set(false);
        this.router.navigate(['news', res.data!.id]);
      },
      error: () => {
        this.loading.set(false);
      },
    });
  }

  /**
   * Gestisce l'aggiornamento di una news esistente.
   * Invia i dati modificati al server tramite HTTP PUT e aggiorna il breadcrumb.
   * @returns void
   */
  override onDataUpdate() {
    this.loading.set(true);
    this.orderItems();
    this.prepareItemsForSubmit();
    const news = this.baseForm.getRawValue();
    this.messageService.addLoadingMessage('loading');

    this.newsService.update(this.newsId(), news).subscribe({
      next: (res) => {
        this.messageService.addSuccessMessage('NEWS.updateSuccess');
        const breadcrumbData = res.data?.title;
        this.breadcrumbService.setBreadcrumbData(breadcrumbData);
        this.breadcrumbService.setIsLoading(false);
        this.loading.set(false);
      },
      error: () => {
        this.loading.set(false);
      },
    });
  }

  /**
   * Prepara gli items per l'invio al server convertendo le FileList in IImage[]
   */
  private prepareItemsForSubmit(): void {
    this.items.controls.forEach((itemControl, index) => {
      const type = itemControl.get('type')?.value;

      if (type === articleItemType.images || type === articleItemType.videos) {
        const manager = this.getItemUploadManager(index);
        const images: any[] = manager.uploadService.FileList;
        itemControl.get('content')?.patchValue(images);
      }
    });
  }

  openNewTagModal() {
    const modalRef = this.modalService.customModal({
      title: this.translateService.instant('TAGS.createTag'),
      content: TagCreateUpdateComponent,
      confirmFn: () => {},
      footer: null, // Inizialmente null
      closable: true,
      maskClosable: false,
      fullScreen: false,
      className: 'custom-modal',
      componentData: {
        crudMode: crudActionType.create,
      },
    });

    // Ascolta l'evento di ready del componente per aggiornare il footer
    modalRef.componentInstance.componentReady.subscribe(
      (footerTemplate: TemplateRef<any>) => {
        this.modalService.updateModal({
          nzFooter: footerTemplate,
        });
      },
    );
  }

  onStatusChange(value: articleStatusType) {
    this.baseForm.get('status')?.patchValue(value);
  }

  /**
   * Gestisce il cambio di tipo di un item
   */
  onItemTypeChange(index: number, newType: articleItemType) {
    const itemControl = this.items.at(index);

    // Resetto i validatori
    itemControl.get('content')?.clearValidators();

    if (newType === articleItemType.text) {
      itemControl.get('content')?.patchValue('');
      // Aggiungo i validatori
      this.setItemValidators(
        newType,
        itemControl.get('content') as FormControl<any>,
      );

      // Crea un nuovo editor se non esiste già
      if (!this.getEditorForPosition(index)) {
        this.editors.update((editors) => [
          ...editors,
          { position: index, editor: new Editor() },
        ]);
      }
    }

    if (newType === articleItemType.images) {
      // Rimuovo eventuali editor se presenti nella stessa posizione
      this.editors.update((editors) => {
        const editorToDestroy = editors.find((e) => e.position === index);
        if (editorToDestroy && editorToDestroy.editor.view) {
          editorToDestroy.editor.destroy();
        }

        return editors.filter((e) => e.position !== index);
      });

      // Rimuovi eventuali file caricati in precedenza
      this.getItemUploadManager(index).uploadService.clean();

      this.cdr.detectChanges();

      itemControl.get('content')?.patchValue([]);

      // Aggiungo i validatori
      this.setItemValidators(
        newType,
        itemControl.get('content') as FormControl<any>,
      );

      // Inizializza l'upload manager per questo item
      this.getItemUploadManager(index);
    }
  }

  addNewItem(data?: IArticleItem) {
    const newIndex = this.items?.length;
    const newItem = this.createNewItem(newIndex, data);
    this.items.push(newItem);

    // Sottoscrivi ai cambiamenti di tipo per il nuovo item
    newItem
      .get('type')
      ?.valueChanges.pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((newType: articleItemType) => {
        this.onItemTypeChange(newIndex, newType);
      });
  }

  drop(event: any) {
    const previousIndex = event.previousIndex;
    const currentIndex = event.currentIndex;

    // Riordina i controlli del FormArray
    moveItemInArray(this.items.controls, previousIndex, currentIndex);

    // Riordina gli editor
    this.reorderEditorsAfterDrop(previousIndex, currentIndex);

    // Riordina gli upload manager
    this.reorderItemUploadManagers(previousIndex, currentIndex);

    // Aggiorna gli ordini
    this.orderItems();
  }

  /**
   * Riordina gli editor dopo il drag & drop
   */
  private reorderEditorsAfterDrop(
    previousIndex: number,
    currentIndex: number,
  ): void {
    this.editors.update((editors) => {
      // Crea una copia dell'array
      const newEditors = [...editors];

      // Trova l'editor da spostare
      const editorToMove = newEditors.find((e) => e.position === previousIndex);

      if (editorToMove) {
        // Rimuovi l'editor dalla posizione precedente
        const filteredEditors = newEditors.filter(
          (e) => e.position !== previousIndex,
        );

        // Aggiorna le posizioni di tutti gli editor
        const reorderedEditors = filteredEditors.map((editor) => {
          if (
            editor.position > previousIndex &&
            editor.position <= currentIndex
          ) {
            return { ...editor, position: editor.position - 1 };
          } else if (
            editor.position < previousIndex &&
            editor.position >= currentIndex
          ) {
            return { ...editor, position: editor.position + 1 };
          }
          return editor;
        });

        // Inserisci l'editor nella nuova posizione
        reorderedEditors.push({ ...editorToMove, position: currentIndex });

        // Ordina per posizione
        return reorderedEditors.sort((a, b) => a.position - b.position);
      }

      return newEditors;
    });
  }

  createNewItem(
    orderNum = 0,
    data?: IArticleItem,
  ): FormGroup<{
    type: FormControl<articleItemType>;
    order: FormControl<number>;
    content: FormControl<any>;
  }> {
    if (data?.type === articleItemType.text) {
      // Crea editor per item di tipo text
      this.editors.update((editors) => [
        ...editors,
        { position: orderNum, editor: new Editor() },
      ]);
    }

    if (
      data?.type === articleItemType.images ||
      data?.type === articleItemType.videos
    ) {
      // Crea upload manager per item di tipo images
      this.getItemUploadManager(orderNum);
    }

    return new FormGroup({
      type: this.fb.nonNullable.control(data?.type || articleItemType.text, [
        Validators.required,
      ]),
      order: this.fb.nonNullable.control(orderNum, [Validators.required]),
      content: this.fb.nonNullable.control<any>(data?.content || '', [
        Validators.required,
      ]),
    });
  }

  deleteItem(index: number) {
    // Rimuovi l'item dal FormArray
    this.items.removeAt(index);

    // Rimuovi l'editor corrispondente
    this.editors.update((editors) => {
      const editorToDestroy = editors.find((e) => e.position === index);
      if (editorToDestroy) {
        editorToDestroy.editor.destroy();
      }
      // Filtra per posizione, non per indice dell'array
      return editors.filter((e) => e.position !== index);
    });

    // Rimuovi l'upload manager corrispondente
    this.removeItemUploadManager(index);

    // Riordina gli indici degli editor e upload manager rimanenti
    this.reorderEditorsAfterDeletion(index);
    this.reorderUploadManagersAfterDeletion(index);
  }

  /**
   * Riordina gli editor dopo l'eliminazione di un item
   */
  private reorderEditorsAfterDeletion(deletedIndex: number): void {
    this.editors.update((editors) =>
      editors.map((editor) => ({
        ...editor,
        position:
          editor.position > deletedIndex
            ? editor.position - 1
            : editor.position,
      })),
    );
  }

  /**
   * Riordina gli upload manager dopo l'eliminazione di un item
   */
  private reorderUploadManagersAfterDeletion(deletedIndex: number): void {
    const managers = this.itemUploadManagers();
    const newManagers = new Map<number, IItemUploadManager>();

    managers.forEach((manager, index) => {
      if (index > deletedIndex) {
        newManagers.set(index - 1, manager);
      } else if (index < deletedIndex) {
        newManagers.set(index, manager);
      }
    });

    this.itemUploadManagers.set(newManagers);
  }

  /**
   * Ottiene la FileList per un item specifico
   */
  getItemFileList(index: number): NzUploadFile[] {
    return this.getItemUploadManager(index).fileList;
  }

  /**
   * Ottiene la ImageList per un item specifico
   */
  getItemImageList(index: number): IImage[] {
    return this.getItemUploadManager(index).imageList;
  }

  /**
   * Gestisce l'upload per un item specifico
   */
  getItemUploadRequest(index: number) {
    return (item: NzUploadXHRArgs) => {
      const manager = this.getItemUploadManager(index);
      return this.imageService.uploadFileLocal(item, IMAGE_LIMITS).subscribe({
        next: (uploadResult) => {
          log('UPLOAD OK for item', index, uploadResult);
          of(uploadResult)
            .pipe(delay(200))
            .subscribe(() => {
              // Aggiorna la FileList del manager con il thumbUrl
              manager.uploadService.FileList =
                manager.uploadService.FileList.map((file) => ({
                  ...file,
                  thumbUrl: file.response?.base64,
                }));

              // Aggiorna il content del form item con le immagini
              const itemControl = this.items.at(index);
              const updatedImages: any[] = manager.uploadService.FileList;

              // Aggiorna anche la imageList del manager
              manager.imageList = updatedImages;

              // Aggiorna il FormControl
              itemControl.get('content')?.patchValue(updatedImages);

              log('Updated item content for index', index, updatedImages);
            });
        },
        error: (err) => {
          log('ERROR IMAGE for item', index, err);
        },
        complete: () => {
          log(
            'IMAGE FILE LIST for item',
            index,
            manager.uploadService.FileList,
          );
        },
      });
    };
  }
  customVideoUploadRequest(index: number) {
    return (item: NzUploadXHRArgs) => {
      const manager = this.getItemUploadManager(index);

      // Create a fake upload with progress simulation
      const uploadObservable = new Observable((observer) => {
        const file = item.file;
        const totalSize = file.size || 1024 * 1024; // Default 1MB
        let uploaded = 0;

        // Simulate upload progress
        const progressInterval = setInterval(() => {
          uploaded += Math.random() * (totalSize * 0.1);

          if (uploaded >= totalSize) {
            uploaded = totalSize;
            clearInterval(progressInterval);

            // Convert file to blob - ensure file is a File object
            let videoBlob: Blob;
            if (file instanceof File) {
              videoBlob = new Blob([file], { type: file.type });
            } else {
              // If not a File, create a blob from the file data
              videoBlob = new Blob([file as any], {
                type: file.type || 'video/mp4',
              });
            }

            const blobUrl = URL.createObjectURL(videoBlob);

            // Create fake response
            const fakeResponse = {
              ok: true,
              data: file,
              blob: videoBlob,
              blobUrl: blobUrl,
              status: 'success',
            };

            // Update the file with response data
            const updatedFile: NzUploadFile = {
              ...file,
              status: 'done' as const,
              response: fakeResponse,
              thumbUrl: blobUrl, // Use blob URL for thumbnail
              url: blobUrl, // Use blob URL for preview
            };

            // Update the upload manager file list
            if (manager && manager.uploadService) {
              manager.uploadService.FileList =
                manager.uploadService.FileList.map((f) =>
                  f.uid === file.uid ? updatedFile : f,
                );
            }

            // Update the form content
            const itemControl = this.items.at(index);
            const updatedVideos: any[] = manager.uploadService.FileList;
            itemControl.get('content')?.patchValue(updatedVideos);

            // Call success callback
            item.onSuccess(fakeResponse, file, null);

            console.log('Video uploaded successfully with blob:', videoBlob);
            console.log('Blob URL created:', blobUrl);

            observer.next(updatedFile);
            observer.complete();
          } else {
            // Report progress
            const progress = Math.round((uploaded / totalSize) * 100);
            item.onProgress({ percent: progress }, file);
          }
        }, 100);

        // Cleanup on unsubscribe
        return () => {
          clearInterval(progressInterval);
        };
      });

      // Return subscription as expected by nzCustomRequest
      return uploadObservable.subscribe();
    };
  }

  downloadFile(file: NzUploadFile): void {
    console.log('DOWNLOAD FILE', file);

    // Try to get blob URL from response
    let downloadUrl = '';

    if (file.response?.blobUrl) {
      downloadUrl = file.response.blobUrl;
    } else if (file.thumbUrl) {
      downloadUrl = file.thumbUrl;
    } else if (file.url) {
      downloadUrl = file.url;
    }

    if (downloadUrl) {
      // Create a temporary anchor element to trigger download
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = file.name || 'video';
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } else {
      console.warn('No download URL available for file:', file);
    }
  }

  /**
   * Gestisce la rimozione di un file per un item specifico
   */
  getItemRemoveHandler(index: number) {
    return (file: NzUploadFile): boolean => {
      const manager = this.getItemUploadManager(index);
      manager.uploadService.removeFiles(file);

      // Aggiorna il content del form item
      const itemControl = this.items.at(index);
      const updatedImages: any[] = manager.uploadService.FileList;

      // Aggiorna anche la imageList del manager
      manager.imageList = updatedImages;

      // Aggiorna il FormControl
      itemControl.get('content')?.patchValue(updatedImages);

      log('Updated item content after removal for index', index, updatedImages);

      return false;
    };
  }

  /**
   * Ottiene l'editor per una specifica posizione
   */
  getEditorForPosition(position: number): Editor | null {
    const editorData = this.editors().find((e) => e.position === position);
    return editorData ? editorData.editor : null;
  }

  /**
   * Cleanup del componente alla distruzione.
   * Resetta il breadcrumb service allo stato iniziale.
   * Distrugge il text editor e pulisce gli upload manager.
   * @returns void
   */
  ngOnDestroy(): void {
    this.breadcrumbService.reset();
    this.editors().forEach((editor) => editor.editor.destroy());

    // Pulisci tutti gli upload manager
    this.itemUploadManagers().forEach((manager) => {
      manager.uploadService.clean();
    });
  }

  /**
   * Demonstrates thumbnail generation for a video file
   * @param file Video file
   */
  generateVideoThumbnail(file: File): void {
    if (!(file instanceof File)) {
      console.warn('File is not a File object');
      return;
    }

    // Create blob URL for the video
    const blobUrl = URL.createObjectURL(file);

    // Generate single thumbnail at 1 second
    this.videoService
      .generateVideoThumbnail(file, 1)
      .then((thumbnail) => {
        console.log(
          'Single thumbnail generated:',
          thumbnail.substring(0, 50) + '...',
        );

        // You can use this thumbnail for preview
        // For example, update the file's thumbUrl
        console.log('Thumbnail base64 length:', thumbnail.length);
      })
      .catch((error) => {
        console.error('Failed to generate thumbnail:', error);
      });

    // Generate multiple thumbnails at different time points
    this.videoService
      .generateMultipleThumbnails(file, [1, 5, 10])
      .then((thumbnails) => {
        console.log('Multiple thumbnails generated:', thumbnails.length);
        thumbnails.forEach((thumbnail, index) => {
          console.log(
            `Thumbnail ${index + 1}:`,
            thumbnail.substring(0, 50) + '...',
          );
        });
      })
      .catch((error) => {
        console.error('Failed to generate multiple thumbnails:', error);
      });

    // Generate thumbnail with both base64 and blob
    this.videoService
      .generateThumbnailWithBlob(file)
      .then((thumbnailData) => {
        console.log('Thumbnail with blob generated');
        console.log('Base64 length:', thumbnailData.base64.length);
        console.log('Blob size:', thumbnailData.blob.size);
        console.log('Blob type:', thumbnailData.blob.type);
      })
      .catch((error) => {
        console.error('Failed to generate thumbnail with blob:', error);
      });

    // Clean up blob URL
    setTimeout(() => {
      URL.revokeObjectURL(blobUrl);
    }, 1000);
  }

  /**
   * Ottiene la lista dei video per un item specifico
   */
  getItemVideoList(index: number): any[] {
    const manager = this.getItemUploadManager(index);
    return manager?.uploadService?.FileList || [];
  }

  /**
   * Ottiene la richiesta di upload per i video di un item specifico
   */
  getItemVideoUploadRequest(index: number) {
    return (item: NzUploadXHRArgs) => {
      const manager = this.getItemUploadManager(index);

      // Create a fake upload with progress simulation
      const uploadObservable = new Observable((observer) => {
        const file = item.file;
        const totalSize = file.size || 1024 * 1024; // Default 1MB
        let uploaded = 0;

        // Simulate upload progress
        const progressInterval = setInterval(() => {
          uploaded += Math.random() * (totalSize * 0.1);

          if (uploaded >= totalSize) {
            uploaded = totalSize;
            clearInterval(progressInterval);

            // Convert file to blob - ensure file is a File object
            let videoBlob: Blob;
            if (file instanceof File) {
              videoBlob = new Blob([file], { type: file.type });
            } else {
              // If not a File, create a blob from the file data
              videoBlob = new Blob([file as any], {
                type: file.type || 'video/mp4',
              });
            }

            const blobUrl = URL.createObjectURL(videoBlob);

            // Generate thumbnail from video using VideoService
            if (file instanceof File) {
              this.videoService
                .generateThumbnailWithBlob(file)
                .then((thumbnailData) => {
                  // Create fake response with thumbnail
                  const fakeResponse = {
                    ok: true,
                    data: file,
                    blob: videoBlob,
                    blobUrl: blobUrl,
                    thumbnail: thumbnailData.base64,
                    thumbnailBlob: thumbnailData.blob,
                    status: 'success',
                  };

                  // Update the file with response data
                  const updatedFile: NzUploadFile = {
                    ...file,
                    status: 'done' as const,
                    response: fakeResponse,
                    thumbUrl: thumbnailData.base64, // Use thumbnail for thumbnail
                    url: blobUrl, // Use blob URL for preview
                  };

                  // Update the upload manager file list
                  if (manager && manager.uploadService) {
                    manager.uploadService.FileList =
                      manager.uploadService.FileList.map((f) =>
                        f.uid === file.uid ? updatedFile : f,
                      );
                  }

                  // Update the form content
                  const itemControl = this.items.at(index);
                  const updatedVideos: any[] = manager.uploadService.FileList;
                  itemControl.get('content')?.patchValue(updatedVideos);

                  // Call success callback
                  item.onSuccess(fakeResponse, file, null);

                  console.log(
                    'Video uploaded successfully with blob:',
                    videoBlob,
                  );
                  console.log('Blob URL created:', blobUrl);
                  console.log(
                    'Thumbnail generated:',
                    thumbnailData.base64.substring(0, 50) + '...',
                  );

                  observer.next(updatedFile);
                  observer.complete();
                })
                .catch((error) => {
                  console.error('Failed to generate thumbnail:', error);

                  // Continue without thumbnail if generation fails
                  const fakeResponse = {
                    ok: true,
                    data: file,
                    blob: videoBlob,
                    blobUrl: blobUrl,
                    status: 'success',
                  };

                  const updatedFile: NzUploadFile = {
                    ...file,
                    status: 'done' as const,
                    response: fakeResponse,
                    thumbUrl: blobUrl, // Fallback to blob URL
                    url: blobUrl,
                  };

                  // Update the upload manager file list
                  if (manager && manager.uploadService) {
                    manager.uploadService.FileList =
                      manager.uploadService.FileList.map((f) =>
                        f.uid === file.uid ? updatedFile : f,
                      );
                  }

                  // Update the form content
                  const itemControl = this.items.at(index);
                  const updatedVideos: any[] = manager.uploadService.FileList;
                  itemControl.get('content')?.patchValue(updatedVideos);

                  // Call success callback
                  item.onSuccess(fakeResponse, file, null);

                  observer.next(updatedFile);
                  observer.complete();
                });
            } else {
              // Handle case where file is not a File object
              const fakeResponse = {
                ok: true,
                data: file,
                blob: videoBlob,
                blobUrl: blobUrl,
                status: 'success',
              };

              const updatedFile: NzUploadFile = {
                ...file,
                status: 'done' as const,
                response: fakeResponse,
                thumbUrl: blobUrl, // Fallback to blob URL
                url: blobUrl,
              };

              // Update the upload manager file list
              if (manager && manager.uploadService) {
                manager.uploadService.FileList =
                  manager.uploadService.FileList.map((f) =>
                    f.uid === file.uid ? updatedFile : f,
                  );
              }

              // Update the form content
              const itemControl = this.items.at(index);
              const updatedVideos: any[] = manager.uploadService.FileList;
              itemControl.get('content')?.patchValue(updatedVideos);

              // Call success callback
              item.onSuccess(fakeResponse, file, null);

              observer.next(updatedFile);
              observer.complete();
            }
          } else {
            // Report progress
            const progress = Math.round((uploaded / totalSize) * 100);
            item.onProgress({ percent: progress }, file);
          }
        }, 100);

        // Cleanup on unsubscribe
        return () => {
          clearInterval(progressInterval);
        };
      });

      // Return subscription as expected by nzCustomRequest
      return uploadObservable.subscribe();
    };
  }
}
