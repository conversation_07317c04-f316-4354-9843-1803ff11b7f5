<nz-form-item [formGroup]="parentForm()">
  @if (!!label) {
    <nz-form-label
      nzLabelAlign="right"
      [nzRequired]="isRequired()"
      nzNoColon="true"
    >
      {{ label }}
    </nz-form-label>
  }
  <nz-form-control nzHasFeedback [nzErrorTip]="errorTpl">
    <textarea
      nz-input
      [formControlName]="controlName()"
      [placeholder]="placeholder()"
      [nzAutosize]="size()"
      [minlength]="minLength()"
      [maxlength]="maxLength()"
    ></textarea>
    <ng-template #errorTpl let-control>
      @if (control.touched && control.hasError("required")) {
        {{ "Campo obbligatorio" }}
      }
      @if (control.touched && control.hasError("minlength")) {
        {{ "Inserisci almeno " + minLength() + " caratteri" }}
      }
      @if (control.touched && control.hasError("maxlength")) {
        {{ "Hai superato il limite di caratteri" }}
      }
    </ng-template>
  </nz-form-control>
</nz-form-item>
