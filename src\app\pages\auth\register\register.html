<div class="container">
  <nz-spin [nzSpinning]="isLoading()">
    <h1 class="m-0">{{ "REGISTER.register" | translate }}</h1>
    <p class="subtitle">{{ "REGISTER.subtitle" | translate }}</p>
    <form
      nz-form
      [nzLayout]="'vertical'"
      [formGroup]="registerForm"
      class="register-form"
    >
      <div nz-row [nzGutter]="16">
        <div nz-col nzSpan="24">
          <div nz-row [nzGutter]="16">
            <div nz-col nzSpan="24" nzLg="12">
              <app-input-generic
                [controlName]="'email'"
                [parentForm]="registerForm"
                [placeholder]="'<EMAIL>'"
                [label]="'REGISTER.email' | translate"
                [type]="'email'"
                [prefixIcon]="'mail'"
                [minLength]="5"
                [maxLength]="50"
              ></app-input-generic>
            </div>
          </div>
        </div>
        <div nz-col nzSpan="24" nzLg="12">
          <app-input-generic
            [controlName]="'name'"
            [parentForm]="registerForm"
            [placeholder]="'Nome'"
            [label]="'REGISTER.name' | translate"
            [type]="'text'"
            [prefixIcon]="'user'"
            [minLength]="5"
            [maxLength]="50"
          ></app-input-generic>
        </div>

        <div nz-col nzSpan="24" nzLg="12">
          <app-input-generic
            [controlName]="'surname'"
            [parentForm]="registerForm"
            [placeholder]="'Cognome'"
            [label]="'REGISTER.surname' | translate"
            [type]="'text'"
            [prefixIcon]="'user'"
            [minLength]="5"
            [maxLength]="50"
          ></app-input-generic>
        </div>

        <div nz-col nzSpan="24" nzLg="12">
          <app-input-password
            [controlName]="'password'"
            [parentForm]="registerForm"
            [placeholder]="'********'"
            [prefixIcon]="'lock'"
            [label]="'REGISTER.password' | translate"
            [minLength]="9"
            [maxLength]="50"
          ></app-input-password>
        </div>

        <div nz-col nzSpan="24" nzLg="12">
          <app-input-password
            [parentForm]="registerForm"
            [controlName]="'confirmPassword'"
            [label]="'REGISTER.confirmPassword' | translate"
            [labelPosition]="'top'"
            [prefixIcon]="'lock'"
            [label]="'REGISTER.confirmPassword' | translate"
            [minLength]="9"
            [maxLength]="50"
          ></app-input-password>
        </div>
      </div>

      <div nz-row class="register-form-margin">
        <div nz-col [nzSpan]="12">
          <!--        <label nz-checkbox [formControlName]="'remember'">{{-->
          <!--          'LOGIN.rememberMe' | translate-->
          <!--        }}</label>-->
        </div>
        <div class="register-form-forgot" nz-col [nzSpan]="12">
          <a
            (click)="onForgotPasswordClick()"
            (keyup)="onForgotPasswordClick()"
            tabindex="0"
            >{{ "REGISTER.forgotPassword" | translate }}</a
          >
        </div>
      </div>
    </form>

    <app-simple-button
      [type]="'primary'"
      [disabled]="!registerForm.valid || isLoading()"
      (onButtonClick)="onRegisterClick()"
      [title]="'REGISTER.register' | translate | uppercase"
      [style]="{ width: '100%' }"
      [autoMinify]="false"
      data-cy="register-submit-button"
    ></app-simple-button>

    @if (registerError()) {
      <div class="error-message">
        <span nz-typography nzType="danger">{{
          "REGISTER.registerError" | translate
        }}</span>
      </div>
    }

    <nz-divider class="divider"></nz-divider>
    <app-simple-button
      [type]="'link'"
      (onButtonClick)="onRegisterClick()"
      [title]="'LOGIN.login' | translate"
      [style]="{ width: '100%', 'font-size': '16px' }"
      [autoMinify]="false"
    ></app-simple-button>
  </nz-spin>
</div>
