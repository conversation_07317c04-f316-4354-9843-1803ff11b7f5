import { articleItemType, articleStatusType } from '@models/enums/article';
import { IImage } from './image';
import { ITag } from './tag';

export interface IArticle {
  title: string;
  summary: string;
  images: IImage[];

  url: string;

  status: articleStatusType;
  pinned: boolean;
  tags: ITag[] | string[];
  items: IArticleItem[];

  orderDate: Date;
  createdAt: Date;
  updatedAt: Date;
  publishedAt?: Date;
}

export interface IArticleItem {
  type: articleItemType;
  order: number;
  content?: string | IImage[];
}
