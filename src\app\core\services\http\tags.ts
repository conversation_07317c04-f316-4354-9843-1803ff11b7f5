import { HttpClient } from '@angular/common/http';
import { inject, Injectable, signal } from '@angular/core';
import { environment } from '@env/environment';
import {
  IBaseResponse,
  IFindResponseMeta,
} from '@models/interfaces/base-response';
import { ICategory } from '@models/interfaces/category';
import { CrudApiOperations } from '@models/interfaces/crud-api';
import { ITag } from '@models/interfaces/tag';
import { IRequestFilter, IRequestMeta } from '@shared/table/types/table.query';
import { Observable, tap } from 'rxjs';

/**
 * Servizio per la gestione delle categorie di prodotto.
 * Fornisce CRUD e stato locale per le categorie tramite API.
 */
@Injectable({
  providedIn: 'root',
})
export class TagsService implements CrudApiOperations<ITag, string> {
  // SERVICES
  private http = inject(HttpClient);

  // VARIABLES
  private _baseTagsApi = `${environment.api.tags}`;

  private _tag = signal<ITag | undefined>(undefined);
  readonly tag$ = this._tag.asReadonly();

  private _tags = signal<ITag[] | undefined>(undefined);
  readonly tags$ = this._tags.asReadonly();

  private _tagId = signal<string | undefined>(undefined);
  readonly tagId$ = this._tagId.asReadonly();

  /**
   * Imposta l'ID della categoria attiva nello stato locale.
   * @param categoryId ID della categoria da impostare come attiva
   * @returns void
   */
  public setTagId(tagId: string): void {
    this._tagId.set(tagId);
  }

  /**
   * Imposta la categoria attiva nello stato locale.
   * @param category Categoria da impostare come attiva
   * @returns void
   */
  public setTag(tag: ITag | undefined): void {
    this._tag.set(tag);
  }

  /**
   * Imposta la lista delle categorie nello stato locale.
   * @param categories Array di categorie da impostare
   * @returns void
   */
  public setTags(tags: ITag[]): void {
    this._tags.set(tags);
  }

  /**
   * Crea una nuova categoria tramite API.
   * @param category Oggetto categoria da creare
   * @returns Observable con la risposta della creazione
   */
  create(tag: ITag) {
    return this.http.post<IBaseResponse<ITag>>(`${this._baseTagsApi}`, tag);
  }

  /**
   * Aggiorna una categoria esistente tramite API.
   * @param categoryId ID della categoria da aggiornare
   * @param category Nuovi dati della categoria
   * @returns Observable con la risposta dell'aggiornamento
   */
  update(tagId: string, tag: ITag) {
    return this.http.put<IBaseResponse<ITag>>(
      `${this._baseTagsApi}/${tagId}`,
      tag,
    );
  }

  /**
   * Elimina una categoria tramite API.
   * @param categoryId ID della categoria da eliminare
   * @returns Observable con la risposta dell'eliminazione
   */
  delete(tagId: string) {
    return this.http.delete<void>(`${this._baseTagsApi}/${tagId}`);
  }

  /**
   * Recupera una singola categoria tramite API e aggiorna lo stato locale.
   * @param categoryId ID della categoria da recuperare
   * @returns Observable con la categoria trovata
   */
  readOne(tagId: string) {
    return this.http
      .get<IBaseResponse<ITag>>(`${this._baseTagsApi}/${tagId}`)
      .pipe(
        tap((value) => {
          this.setTag(value.data!);
        }),
      );
  }

  /**
   * Recupera tutte le categorie tramite API e aggiorna lo stato locale.
   * @returns Observable con la lista delle categorie
   */
  readAll() {
    return this.http.get<IBaseResponse<ITag[]>>(`${this._baseTagsApi}`).pipe(
      tap((value) => {
        this.setTags(value.data!);
      }),
    );
  }

  /**
   * Recupera prodotti tramite ricerca per nome.
   * @param tagName Nome del prodotto da cercare
   * @returns Observable con la lista dei prodotti trovati
   */
  readTagsByName(tagName: string): Observable<IBaseResponse<ITag[]>> {
    return this.http.get<IBaseResponse<ITag[]>>(
      `${this._baseTagsApi}/search?name=${tagName}`,
    );
  }

  /**
   * Esegue una ricerca avanzata sulle categorie tramite API.
   * @param meta Metadati di paginazione/ordinamento
   * @param filter Filtri di ricerca
   * @returns Observable con la lista delle categorie e metadati
   */
  search(
    meta: IRequestMeta,
    filter: IRequestFilter[],
  ): Observable<IBaseResponse<ICategory[], IFindResponseMeta>> {
    return this.http.post<IBaseResponse<ICategory[], IFindResponseMeta>>(
      `${this._baseTagsApi}/search`,
      {
        meta,
        filter,
      },
    );
  }
}
