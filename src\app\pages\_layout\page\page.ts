import { NgTemplateOutlet } from '@angular/common';
import { Component, inject, input, signal, TemplateRef } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NavigationEnd, Router, RouterLink } from '@angular/router';
import { RolePermissionDirective } from '@core/directives/role-permission';
import { LayoutService } from '@core/services/utils/layout';
import { GenericUtils } from '@core/utils/generic';
import { environment } from '@env/environment';
import { crudActionType } from '@models/enums/crud-action-type';
import { ISection } from '@models/interfaces/menu';
import { TranslateModule } from '@ngx-translate/core';
import { NzDividerComponent } from 'ng-zorro-antd/divider';
import { NzIconDirective } from 'ng-zorro-antd/icon';
import {
  NzContentComponent,
  NzHeaderComponent,
  NzLayoutComponent,
  NzSiderComponent,
} from 'ng-zorro-antd/layout';
import {
  NzMenuDirective,
  NzMenuItemComponent,
  NzSubMenuComponent,
} from 'ng-zorro-antd/menu';
import { NzTooltipDirective } from 'ng-zorro-antd/tooltip';
import { FooterComponent } from '../footer/footer';
import { HeaderComponent } from '../header/header';
import { TopBarComponent } from '../top-bar/top-bar';

@Component({
  selector: 'app-page',
  standalone: true,
  imports: [
    NzLayoutComponent,
    NzSiderComponent,
    NzHeaderComponent,
    NzContentComponent,
    NzMenuItemComponent,
    NzSubMenuComponent,
    TopBarComponent,
    FooterComponent,
    HeaderComponent,
    NzMenuDirective,
    NzTooltipDirective,
    TranslateModule,
    NzDividerComponent,
    RouterLink,
    NzIconDirective,
    NgTemplateOutlet,
    RolePermissionDirective,
  ],
  templateUrl: './page.html',
  styleUrl: './page.less',
})
export class PageComponent {
  // SERVICES
  private router = inject(Router);
  private layoutService = inject(LayoutService);

  // INPUTS
  tplContent = input<TemplateRef<any>>();

  // VARIABLES
  protected menus = signal<ISection[]>(environment.sections);
  protected isCollapsed = signal<boolean>(true);
  protected crudActionType = crudActionType;
  protected isMobile = signal<boolean>(false);

  constructor() {
    this.isMenuSelected(this.menus());
    localStorage.getItem(GenericUtils.menu_status) === 'false'
      ? this.isCollapsed.set(false)
      : this.isCollapsed.set(true);

    this.router.events.pipe(takeUntilDestroyed()).subscribe((event) => {
      if (event instanceof NavigationEnd) {
        this.isMenuSelected(this.menus());
      }
    });
  }

  isMenuSelected(menu: ISection[], parent?: ISection) {
    for (let index = 0; index < menu.length; index++) {
      if (menu[index].children) {
        this.isMenuSelected(menu[index].children!, menu[index]);
      }
      menu[index].selected = this.isRouterActive(menu[index]);
      if (parent) parent.open = menu[index].selected;
    }
  }

  onSiderCollapse() {
    this.isCollapsed.update((prev) => !prev);
    localStorage.setItem(
      GenericUtils.menu_status,
      this.isCollapsed().toString(),
    );
  }

  onMenuClick(menu: ISection) {
    this.router.navigate([menu.routerLink]);
  }

  isRouterActive(menu: ISection) {
    const isActive = this.router.isActive(menu.routerLink, {
      matrixParams: 'subset',
      queryParams: 'subset',
      paths: 'subset',
      fragment: 'ignored',
    });

    if (isActive) this.layoutService.currentMenu$.set(menu);
    return isActive;
  }
}
