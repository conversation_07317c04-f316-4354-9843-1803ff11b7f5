import { Routes } from '@angular/router';

import { crudActionType } from '@models/enums/crud-action-type';

import { TagCreateUpdateComponent } from './tag-create-update/tag-create-update';
import { TagListComponent } from './tag-list/tag-list';
import { TagComponent } from './tag/tag';

export const TAGS_ROUTES: Routes = [
  { path: '', component: TagListComponent },
  {
    path: 'create',
    component: TagCreateUpdateComponent,
    data: { crudType: crudActionType.create },
  },
  {
    path: ':id',
    component: TagComponent,
    children: [
      {
        path: '',
        redirectTo: 'info',
        pathMatch: 'full',
        data: { crudType: crudActionType.update },
      },
      {
        path: 'info',
        component: TagCreateUpdateComponent,
        data: { crudType: crudActionType.update },
      },
      {
        path: 'products',
        loadComponent: () =>
          import('@pages/products/product-list/product-list').then(
            (m) => m.ProductListComponent,
          ),
        data: {
          isChild: true,
        },
      },
    ],
  },
];
