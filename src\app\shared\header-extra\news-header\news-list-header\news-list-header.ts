import { Component, inject } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { PopoverButtonComponent } from '@shared/buttons/popover-button/popover-button';
import { SimpleButtonComponent } from '@shared/buttons/simple-button/simple-button';
import { NzSpaceComponent, NzSpaceItemDirective } from 'ng-zorro-antd/space';

@Component({
  selector: 'app-news-list-header',
  imports: [
    NzSpaceComponent,
    SimpleButtonComponent,
    NzSpaceItemDirective,
    PopoverButtonComponent,
    TranslateModule,
  ],
  templateUrl: './news-list-header.html',
  styleUrl: './news-list-header.less',
})
export class NewsListHeader {
  private router = inject(Router);

  onAddNewsClick() {
    this.router.navigateByUrl('/news/create');
  }
}
