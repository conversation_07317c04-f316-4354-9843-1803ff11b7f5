import { Component, input } from '@angular/core';
import { articleStatusType } from '@models/enums/article';
import { TranslateModule } from '@ngx-translate/core';
import { NzIconDirective } from 'ng-zorro-antd/icon';
import { NzTagComponent } from 'ng-zorro-antd/tag';

@Component({
  selector: 'app-tag-news-status',
  imports: [NzTagComponent, NzIconDirective, TranslateModule],
  templateUrl: './tag-news-status.html',
  styleUrl: './tag-news-status.less',
})
export class TagNewsStatus {
  readonly value = input.required<articleStatusType>();
  protected text: string;
  protected icon: string;
  protected color: string;

  ngOnChanges(): void {
    this.setTag();
  }

  setTag() {
    switch (this.value()) {
      case articleStatusType.draft:
        this.text = 'NEWS.draft';
        this.icon = 'edit';
        this.color = 'warning';
        break;
      case articleStatusType.published:
        this.text = 'NEWS.published';
        this.icon = 'check-circle';
        this.color = 'success';
        break;
    }
  }
}
