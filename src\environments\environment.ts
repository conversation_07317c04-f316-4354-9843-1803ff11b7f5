import {
  languageCodeType,
  languageCountryCodeType,
} from '@core/services/utils/language';
import { roleType } from '@models/enums/role';
import { ISettings } from '@models/interfaces/configuration';

export const environment: ISettings = {
  production: `${process.env['PRODUCTION']}` === 'true',
  api: {
    base: `${process.env['BASE_API_URL']}`,
    auth: `${process.env['BASE_API_URL']}/api/v1/auth/admin`,
    admin: `${process.env['BASE_API_URL']}/api/v1/admin`,
    products: `${process.env['BASE_API_URL']}/api/v1/products`,
    categories: `${process.env['BASE_API_URL']}/api/v1/categories`,
    collections: `${process.env['BASE_API_URL']}/api/v1/collections`,
    staff: `${process.env['BASE_API_URL']}/api/v1/staff`,
    awards: `${process.env['BASE_API_URL']}/api/v1/awards`,
    news: `${process.env['BASE_API_URL']}/api/v1/news`,
    tags: `${process.env['BASE_API_URL']}/api/v1/tags`,
  },
  languages: [
    {
      name: 'Italiano',
      code: languageCodeType.it,
      countryCode: languageCountryCodeType.IT,
      enabled: true,
      default: true,
      icon: 'client-ui:flag-it',
    },
    {
      name: 'English',
      code: languageCodeType.en,
      countryCode: languageCountryCodeType.GB,
      enabled: true,
      icon: 'client-ui:flag-en',
    },
    {
      name: 'Deutsch',
      code: languageCodeType.de,
      countryCode: languageCountryCodeType.DE,
      enabled: true,
      icon: 'client-ui:flag-de',
    },
  ],
  sections: [
    // {
    //   level: 0,
    //   title: 'dashboard',
    //   id: 'M_0',
    //   icon: 'home',
    //   rolePermission: [roleType.admin, roleType.staff],
    //   routerLink: 'dashboard',
    // },
    {
      level: 0,
      title: 'products',
      id: 'M_1',
      icon: 'client-ui:products',
      rolePermission: [roleType.admin, roleType.staff],
      routerLink: 'products',
      divider: {
        enabled: true,
        text: 'products',
      },
    },
    {
      level: 0,
      title: 'categories',
      id: 'M_2',
      icon: 'client-ui:categories',
      rolePermission: [roleType.admin, roleType.staff],
      routerLink: 'categories',
    },
    {
      level: 0,
      title: 'awards',
      id: 'M_3',
      icon: 'client-ui:awards',
      rolePermission: [roleType.admin, roleType.staff],
      routerLink: 'awards',
    },
    {
      level: 0,
      title: 'news',
      id: 'M_4',
      icon: 'client-ui:news',
      rolePermission: [roleType.admin, roleType.staff],
      routerLink: 'news',
      divider: {
        enabled: true,
        text: 'blog',
      },
    },
    {
      level: 0,
      title: 'events',
      id: 'M_5',
      icon: 'client-ui:events',
      rolePermission: [roleType.admin, roleType.staff],
      routerLink: 'events',
    },
    {
      level: 0,
      title: 'tags',
      id: 'M_6',
      icon: 'client-ui:tags',
      rolePermission: [roleType.admin],
      routerLink: 'tags',
    },

    {
      level: 0,
      title: 'staff',
      id: 'M_7',
      icon: 'client-ui:staff',
      rolePermission: [roleType.admin, roleType.staff],
      routerLink: 'staff',
      divider: {
        enabled: true,
        text: 'staff',
      },
    },
    {
      level: 0,
      title: 'admin',
      id: 'M_8',
      icon: 'client-ui:admin',
      rolePermission: [roleType.admin],
      routerLink: 'admin',
    },
  ],
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/dist/zone-error';  // Included with Angular CLI.
