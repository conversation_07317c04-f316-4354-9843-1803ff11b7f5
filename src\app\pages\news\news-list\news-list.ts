import {
  Component,
  inject,
  signal,
  TemplateRef,
  viewChild,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { trackEvent } from '@aptabase/web';
import { EllipsisDirective } from '@core/directives/ellipsis';
import { DateFormatPipe } from '@core/pipes/date.pipe';
import { NullValuePipe } from '@core/pipes/null-value.pipe';
import { NewsService } from '@core/services/http/news';
import { HeaderService } from '@core/services/utils/header';
import { MessageService } from '@core/services/utils/message';
import { ModalService } from '@core/services/utils/modal';
import { articleStatusType } from '@models/enums/article';
import { currentSectionType } from '@models/enums/current-section';
import { IImage } from '@models/interfaces/image';
import { INews, ITag } from '@models/interfaces/news';
import { TranslateModule } from '@ngx-translate/core';
import { TableComponent } from '@shared/table/table';
import { TableQueryService } from '@shared/table/table-query.service';
import { ITableRowAction } from '@shared/table/types/table.action';
import { ITableColumn } from '@shared/table/types/table.column';
import { ITableQuery } from '@shared/table/types/table.query';
import { IRefreshData } from '@shared/table/types/table.refresh-data';
import { ITableSetting } from '@shared/table/types/table.settings';
import { TagNewsStatus } from '@shared/tags/tag-news-status/tag-news-status';
import {
  NzAvatarComponent,
  NzAvatarGroupComponent,
} from 'ng-zorro-antd/avatar';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzImageModule, NzImageService } from 'ng-zorro-antd/image';
import { NzTagComponent } from 'ng-zorro-antd/tag';
import { NzTooltipDirective, NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzTypographyModule } from 'ng-zorro-antd/typography';

@Component({
  selector: 'app-news-list',
  imports: [
    TableComponent,
    NullValuePipe,
    EllipsisDirective,
    NzImageModule,
    NzAvatarComponent,
    NzAvatarGroupComponent,
    NzToolTipModule,
    TranslateModule,
    NzTypographyModule,
    DateFormatPipe,
    TagNewsStatus,
    NzTagComponent,
    NzIconModule,
    NzTooltipDirective,
    NzTypographyModule,
  ],
  providers: [TableQueryService],
  templateUrl: './news-list.html',
  styleUrl: './news-list.less',
})
export class NewsList {
  // SERVICES
  private headerService = inject(HeaderService);
  private newsService = inject(NewsService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private modalService = inject(ModalService);
  private messageService = inject(MessageService);
  private imageService = inject(NzImageService);

  tplId = viewChild<TemplateRef<any>>('tplId');
  tplImage = viewChild<TemplateRef<any>>('tplImage');
  tplDate = viewChild<TemplateRef<any>>('tplDate');
  tplStatus = viewChild<TemplateRef<any>>('tplStatus');
  tplPinned = viewChild<TemplateRef<any>>('tplPinned');
  tplText = viewChild<TemplateRef<any>>('tplText');
  tplTags = viewChild<TemplateRef<any>>('tplTags');

  protected loading = signal<boolean>(false);
  protected data = signal<any[]>([]);
  protected dataQuery = signal<any>(null);
  protected tableLayoutSettings = signal<ITableSetting>(<ITableSetting>{});
  protected tableQueryRequest = signal<ITableQuery | null>(null);
  protected refreshData = signal<IRefreshData>({
    interval: 0,
    min: 0,
    max: 180,
    step: 30,
  });

  constructor() {
    trackEvent('news_list_page');
    this.headerService.setCurrentSection(currentSectionType.newsList);
    const settings: ITableSetting = {
      singleRowActions: this.buildRowActions(),
      listOfColumns: this.buildTableColumns(),
      dynamicTableHeightOffset: 375,
      pagination: {
        total: 0,
        pageSize: 50,
        pageIndex: 1,
      },
    };
    this.tableLayoutSettings.set(settings);
  }

  getFullTextTag(element: ITag[]): string {
    return element.reduce((acc, tag: ITag, index: number) => {
      return acc + tag.name + (index === element.length - 1 ? '' : ' - ');
    }, '');
  }

  onQueryChange(query: ITableQuery) {
    this.tableQueryRequest.set(query);
    this.loading.set(true);

    this.newsService.search(query.meta, query.filter).subscribe((value) => {
      this.data.set(value.data);

      const currentSettings = this.tableLayoutSettings();
      currentSettings.pagination.total = value.meta!.page.total;
      currentSettings.pagination.pageIndex = value.meta!.page.pageIndex;
      currentSettings.pagination.pageSize = value.meta!.page.pageSize;
      this.tableLayoutSettings.set({ ...currentSettings });
      this.loading.set(false);
    });
  }

  ngAfterViewInit(): void {
    const currentSettings = this.tableLayoutSettings();
    currentSettings.listOfColumns.forEach(
      (col) =>
        (col.cellTemplate = col.cellTemplateName
          ? this[col.cellTemplateName]()
          : null),
    );
    this.tableLayoutSettings.set({ ...currentSettings });
  }

  private buildRowActions(): ITableRowAction[] {
    return [
      {
        label: 'ACTIONS.modify',
        icon: 'edit',
        callbackFn: (row: INews) => {
          this.router.navigate([row.id], { relativeTo: this.route });
        },
      },
      {
        label: 'ACTIONS.delete',
        icon: 'delete',
        callbackFn: (row: INews) => {
          this.modalService.confirmDelete({
            confirmFn: () => {
              this.messageService.addLoadingMessage();
              this.newsService.delete(row.id).subscribe({
                next: () => {
                  this.onQueryChange(this.tableQueryRequest()!);
                  this.messageService.addSuccessMessage('NEWS.deleteSuccess');
                },
              });
            },
            title: 'NEWS.confirmDeleteTitle',
            subtitle: 'NEWS.confirmDeleteSubtitle',
          });
        },
        danger: true,
      },
    ];
  }

  private buildTableColumns(): ITableColumn[] {
    return [
      {
        id: 'id',
        cellTemplate: this.tplId(),
        cellTemplateName: 'tplId',
        title: 'ID',
        sortOrder: null,
        sortFn: true,
        width: '80px',
        lockOnLeft: true,
        visible: true,
      },
      {
        id: 'status',
        cellTemplate: this.tplStatus(),
        cellTemplateName: 'tplStatus',
        title: 'NEWS.status',
        sortOrder: null,
        sortFn: true,
        width: '120px',
        visible: true,
        filterFn: true,
        listOfFilter: [
          {
            value: articleStatusType.draft,
            text: 'NEWS.draft',
          },
          {
            value: articleStatusType.published,
            text: 'NEWS.published',
          },
        ],
      },
      {
        id: 'images',
        cellTemplate: this.tplImage(),
        cellTemplateName: 'tplImage',
        title: 'NEWS.image',
        width: '90px',
        visible: true,
      },
      {
        id: 'title',
        title: 'NEWS.title',
        cellTemplate: this.tplText(),
        cellTemplateName: 'tplText',
        sortOrder: null,
        sortFn: true,
        width: '220px',
        visible: true,
        hasSearchFilter: true,
      },
      {
        id: 'summary',
        title: 'NEWS.summary',
        cellTemplate: this.tplText(),
        cellTemplateName: 'tplText',
        sortOrder: null,
        sortFn: true,
        width: '220px',
        visible: true,
        hasSearchFilter: true,
      },
      {
        id: 'createdAt',
        cellTemplate: this.tplDate(),
        cellTemplateName: 'tplDate',
        title: 'NEWS.createdAt',
        sortOrder: null,
        sortFn: true,
        width: '120px',
        visible: true,
      },
      {
        id: 'updatedAt',
        cellTemplate: this.tplDate(),
        cellTemplateName: 'tplDate',
        title: 'NEWS.updatedAt',
        sortOrder: null,
        sortFn: true,
        width: '120px',
        visible: true,
      },
      {
        id: 'publishedAt',
        cellTemplate: this.tplDate(),
        cellTemplateName: 'tplDate',
        title: 'NEWS.publishedAt',
        sortOrder: null,
        sortFn: true,
        width: '120px',
        visible: true,
      },
      {
        id: 'orderDate',
        cellTemplate: this.tplDate(),
        cellTemplateName: 'tplDate',
        title: 'NEWS.orderDate',
        sortOrder: null,
        sortFn: true,
        width: '120px',
        visible: true,
      },
      {
        id: 'pinned',
        cellTemplate: this.tplPinned(),
        cellTemplateName: 'tplPinned',
        title: 'NEWS.pinned',
        sortOrder: null,
        width: '130px',
        visible: true,
      },
      {
        id: 'tags',
        title: 'NEWS.tags',
        cellTemplate: this.tplTags(),
        cellTemplateName: 'tplTags',
        width: '150px',
        visible: true,
        //TODO Aggiungere funzionalità di ricerca tag
        hasSearchFilter: true,
      },
    ];
  }

  goToDetail(newsId: string) {
    this.router.navigate([newsId], { relativeTo: this.route });
  }

  onImageClick(images: IImage[]): void {
    const imagePreview = images.map((image) => {
      return { src: image.url, alt: image.name };
    });
    this.imageService.preview(imagePreview, { nzZoom: 1, nzRotate: 0 });
  }
}
