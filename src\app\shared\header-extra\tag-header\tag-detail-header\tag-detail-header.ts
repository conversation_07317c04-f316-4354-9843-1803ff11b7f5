import { NgTemplateOutlet } from '@angular/common';
import { Component, computed, DestroyRef, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Router } from '@angular/router';
import { RolePermissionDirective } from '@core/directives/role-permission';
import { TagsService } from '@core/services/http/tags';
import { HeaderService } from '@core/services/utils/header';
import { MessageService } from '@core/services/utils/message';
import { ModalService } from '@core/services/utils/modal';
import { roleType } from '@models/enums/role';
import { TranslateModule } from '@ngx-translate/core';
import { ActionsButtonComponent } from '@shared/buttons/actions-button/actions-button';
import { PopoverButtonComponent } from '@shared/buttons/popover-button/popover-button';
import { ITableRowAction } from '@shared/table/types/table.action';
import { NzSpaceModule } from 'ng-zorro-antd/space';

@Component({
  selector: 'app-tag-detail-header',
  imports: [
    NgTemplateOutlet,
    PopoverButtonComponent,
    TranslateModule,
    NzSpaceModule,
    RolePermissionDirective,
    NgTemplateOutlet,
    ActionsButtonComponent,
  ],
  templateUrl: './tag-detail-header.html',
  styleUrl: './tag-detail-header.less',
})
export class TagDetailHeader {
  // SERVICES
  private headerService = inject(HeaderService);
  private tagService = inject(TagsService);
  private modalService = inject(ModalService);
  private messageService = inject(MessageService);
  private router = inject(Router);
  private destroyRef = inject(DestroyRef);

  // PROPERTIES
  private _tag = this.tagService.tag$;
  protected tplMainButton = this.headerService.template;

  public actions = computed<ITableRowAction[]>(() => {
    return this._tag() ? this.buildRowActions() : ([] as ITableRowAction[]);
  });

  // ENUMS
  roleType = roleType;

  private buildRowActions(): ITableRowAction[] {
    return [
      {
        label: 'ACTIONS.delete',
        icon: 'delete',
        visibilityFn: () => true,
        callbackFn: () => {
          this.modalService.confirmDelete({
            confirmFn: () => {
              this.messageService.addLoadingMessage();
              this.tagService
                .delete(this._tag()?.id)
                .pipe(takeUntilDestroyed(this.destroyRef))
                .subscribe({
                  next: () => {
                    this.router.navigateByUrl('/tags');
                    this.messageService.addSuccessMessage('TAGS.deleteSuccess');
                  },
                });
            },
            title: 'TAGS.confirmDeleteTitle',
            subtitle: 'TAGS.confirmDeleteSubtitle',
          });
        },
        danger: true,
      },
    ];
  }
}
