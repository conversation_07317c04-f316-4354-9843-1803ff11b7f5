import { Routes } from '@angular/router';

import { crudActionType } from '@models/enums/crud-action-type';
import { CategoryCreateUpdateComponent } from './category-create-update/category-create-update';
import { CategoryListComponent } from './category-list/category-list';
import { CategoryComponent } from './category/category';

export const CATEGORIES_ROUTES: Routes = [
  { path: '', component: CategoryListComponent },
  {
    path: 'create',
    component: CategoryCreateUpdateComponent,
    data: { crudType: crudActionType.create },
  },
  {
    path: ':id',
    component: CategoryComponent,
    children: [
      {
        path: '',
        redirectTo: 'info',
        pathMatch: 'full',
        data: { crudType: crudActionType.update },
      },
      {
        path: 'info',
        component: CategoryCreateUpdateComponent,
        data: { crudType: crudActionType.update },
      },
      {
        path: 'products',
        loadComponent: () =>
          import('@pages/products/product-list/product-list').then(
            (m) => m.ProductListComponent,
          ),
        data: {
          isChild: true,
        },
      },
    ],
  },
];
