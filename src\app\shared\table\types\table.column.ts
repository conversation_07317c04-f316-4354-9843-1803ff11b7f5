import { TemplateRef } from '@angular/core';

export interface ITableColumn {
  /** Identifier for column data. It will match with property name of data shown in column */
  id: string;
  /** Title of table column. (It's possible to provide i18n key for runtime translation) */
  title: string;
  /** TemplateRef for custom cell view */
  cellTemplate?: TemplateRef<any>;
  /** Can be used for set cell template without puntual position assignment  */
  cellTemplateName?: string;
  /** Sort direction.
   * @remarks 'ascend' | 'descend' | null */
  sortOrder?: string | 'ascend' | 'descend' | null;
  /** Sort function used to sort the data on client side (ref to Array.sort compareFunction).
   * Should be set to true when using server side sorting
   * @remarks ((a: any, b: any, sortOrder?: string) => number) */
  sortFn?: boolean;
  /** Supported sort order, could be 'ascend', 'descend', null */
  sortDirections?: [string | 'ascend' | 'descend'] | null;
  /** Filter options, text, and value for callback, byDefault to enable filter by default */
  listOfFilter?: { text: string; value: any; byDefault?: boolean }[];
  /** Filter function used to filter the data on client side. Set to true when using server side filtering
   *  @remarks ((value: any, data: any) => boolean;)
   */
  filterFn?: boolean;
  /** Whether multiple mode filtering is enabled */
  filterMultiple?: boolean;
  /** Enable custom search filter */
  hasSearchFilter?: boolean;
  /** Enable date range filter */
  hasDateFilter?: boolean;
  /** Enable date range filter */
  hasSingleDateFilter?: boolean;
  // Show time on dates filter. By default true
  dateFilterShowTime?: boolean;
  /** Custom filter initial value */
  filterValue?: any;
  /** Date filter shortcut predefined date range buttons (now, last24h, lastWeek, ...)*/
  dateFilterShortcuts?: boolean;
  /** For multiple mode filtering. List of Filter can be filtered with input search */
  filtersSearchable?: boolean;
  /** Filter custom operator (find)*/
  filterOperator?: requestFilterOperatorType | requestFilterOperatorType[];
  /** Filter tyopeof (only for find). "timestamp, number, string" */
  filterTypeof?: string;
  /** Set closable flag for enable/disable closable filter (now work only on dateFilter). By default true */
  filterUnclosable?: boolean;
  /** Column width */
  width?: string;
  /** Lock column on left */
  lockOnLeft?: boolean;
  /** Lock column on right */
  lockOnRight?: boolean;
  /** Column label alignment.
   * @remarks See AlignmentType enumeration for values */
  alignment?: alignmentType;
  /** Column visibility */
  visible?: boolean;
  /** If true, all filters & sorters are read only (no user change allowed) */
  readOnly?: boolean;
  /* This is a custom filter function that will translate the active filter value to a string. */
  translateFilterFn?: (item: any | any[]) => string | string[];
  /** Function to format the value without using a template  */
  formatValueFn?: (item: any) => string;
}

/** (Const) Type of http request filter operators as const */
// export const RequestFilterOperatorType: "AND" | "OR" | "LT" | "GT" | "LE" | "GE" | "EQ" | "NOT_EQ" | "NE" |
//     "CI_STRING" | "TAIL_STRING" | "HEAD_STRING" | "PART_STRING" | "PART_STRING_CI" | "FULL_STRING" |
//     "OR_NULL" | "IN" | "NIN" | "NOT_NULL" | "IS_NULL" = "AND";

/** (Enum) Type of http request filter operators */
export enum requestFilterOperatorType {
  and = 'AND',
  or = 'OR',
  lt = 'LT',
  gt = 'GT',
  le = 'LE',
  ge = 'GE',
  eq = 'EQ',
  notEq = 'NOT_EQ',
  ne = 'NE',
  mongoId = 'MONGO_ID',
  fullString = 'FULL_STRING',
  partString = 'PART_STRING',
  orNull = 'OR_NULL',
  in = 'IN',
  nin = 'NIN',
  notNull = 'NOT_NULL',
  isNull = 'IS_NULL',
  arrayContains = 'ARRAY_CONTAINS',
}

/** Enumeration of text alignment types */
export enum alignmentType {
  left = 'left',
  center = 'center',
  right = 'right',
}
