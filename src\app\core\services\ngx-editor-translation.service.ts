import { Injectable, inject } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Injectable({
  providedIn: 'root',
})
export class NgxEditorTranslationService {
  private translateService = inject(TranslateService);

  /**
   * Applica le traduzioni usando JavaScript per modificare direttamente gli elementi
   */
  applyTranslations(): void {
    // Usa un timeout per assicurarsi che l'editor sia renderizzato
    setTimeout(() => {
      this.translateElements();
    }, 500);
  }

  /**
   * Traduce gli elementi dell'editor
   */
  private translateElements(): void {
    // Traduce il dropdown "Heading"
    this.translateHeadingDropdown();

    // Osserva i cambiamenti per tradurre i dialog che appaiono dinamicamente
    this.observeForDialogs();
  }

  /**
   * Traduce il dropdown heading
   */
  private translateHeadingDropdown(): void {
    const headingButton = document.querySelector(
      '.NgxEditor__MenuItem--Dropdown',
    );
    if (headingButton && headingButton.textContent?.includes('Heading')) {
      headingButton.textContent =
        this.translateService.instant('EDITOR.heading');
    }
  }

  /**
   * Osserva i cambiamenti nel DOM per tradurre i dialog
   */
  private observeForDialogs(): void {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element;

            // Traduce il dialog del link se presente
            if (
              element.classList?.contains('NgxEditor__LinkDialog') ||
              element.querySelector('.NgxEditor__LinkDialog')
            ) {
              setTimeout(() => this.translateLinkDialog(element), 100);
            }
          }
        });
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });
  }

  /**
   * Traduce il dialog del link
   */
  private translateLinkDialog(container: Element): void {
    const dialog = container.classList?.contains('NgxEditor__LinkDialog')
      ? container
      : container.querySelector('.NgxEditor__LinkDialog');

    if (!dialog) return;

    // Traduce i placeholder degli input
    const urlInput = dialog.querySelector(
      'input[placeholder*="URL"]',
    ) as HTMLInputElement;
    if (urlInput) {
      urlInput.placeholder = this.translateService.instant('EDITOR.url');
    }

    const textInput = dialog.querySelector(
      'input[placeholder*="Text"]',
    ) as HTMLInputElement;
    if (textInput) {
      textInput.placeholder = this.translateService.instant('EDITOR.text');
    }

    // Traduce il testo del checkbox
    const checkboxLabel = dialog.querySelector('label');
    if (checkboxLabel && checkboxLabel.textContent?.includes('new tab')) {
      checkboxLabel.textContent = this.translateService.instant(
        'EDITOR.openInNewTab',
      );
    }

    // Traduce il pulsante
    const button = dialog.querySelector('button');
    if (button && button.textContent?.includes('Insert')) {
      button.textContent = this.translateService.instant('EDITOR.insert');
    }
  }
}
