import { Injectable, inject } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Injectable({
  providedIn: 'root',
})
export class NgxEditorTranslationService {
  private translateService = inject(TranslateService);

  /**
   * Applica le traduzioni all'interfaccia dell'editor usando CSS e DOM manipulation
   */
  applyTranslations(): void {
    // Aspetta che l'editor sia renderizzato
    setTimeout(() => {
      this.translateTooltips();
      this.translateDropdownItems();
      this.translateLinkDialog();
    }, 100);
  }

  /**
   * Traduce i tooltip dei pulsanti della toolbar
   */
  private translateTooltips(): void {
    const tooltipMappings = {
      Bold: 'EDITOR.bold',
      Italic: 'EDITOR.italic',
      Underline: 'EDITOR.underline',
      Strike: 'EDITOR.strike',
      Blockquote: 'EDITOR.blockquote',
      'Ordered List': 'EDITOR.orderedList',
      'Bullet List': 'EDITOR.bulletList',
      Link: 'EDITOR.link',
      'Align Left': 'EDITOR.alignLeft',
      'Align Center': 'EDITOR.alignCenter',
      'Align Right': 'EDITOR.alignRight',
    };

    Object.entries(tooltipMappings).forEach(
      ([originalText, translationKey]) => {
        const elements = document.querySelectorAll(`[title="${originalText}"]`);
        elements.forEach((element) => {
          element.setAttribute(
            'title',
            this.translateService.instant(translationKey),
          );
        });
      },
    );
  }

  /**
   * Traduce gli elementi del dropdown heading
   */
  private translateDropdownItems(): void {
    const headingElements = document.querySelectorAll(
      '.NgxEditor__DropdownItem',
    );
    headingElements.forEach((element) => {
      const textContent = element.textContent?.trim();
      if (textContent === 'Heading') {
        element.textContent = this.translateService.instant('EDITOR.heading');
      }
    });
  }

  /**
   * Traduce il dialog per inserire link
   */
  private translateLinkDialog(): void {
    // Osserva i cambiamenti nel DOM per catturare quando si apre il dialog
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element;

            // Cerca il dialog del link
            const linkDialog =
              element.querySelector('.NgxEditor__LinkDialog') ||
              (element.classList?.contains('NgxEditor__LinkDialog')
                ? element
                : null);

            if (linkDialog) {
              this.translateLinkDialogElements(linkDialog);
            }
          }
        });
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });
  }

  /**
   * Traduce gli elementi specifici del dialog link
   */
  private translateLinkDialogElements(dialog: Element): void {
    // Traduce i placeholder
    const urlInput = dialog.querySelector(
      'input[placeholder="URL"]',
    ) as HTMLInputElement;
    if (urlInput) {
      urlInput.placeholder = this.translateService.instant('EDITOR.url');
    }

    const textInput = dialog.querySelector(
      'input[placeholder="Text"]',
    ) as HTMLInputElement;
    if (textInput) {
      textInput.placeholder = this.translateService.instant('EDITOR.text');
    }

    // Traduce il checkbox
    const checkbox = dialog.querySelector('input[type="checkbox"]');
    if (checkbox) {
      const label = checkbox.parentElement?.querySelector('label');
      if (label && label.textContent?.includes('Open in new tab')) {
        label.textContent = this.translateService.instant(
          'EDITOR.openInNewTab',
        );
      }
    }

    // Traduce il pulsante Insert
    const insertButton = dialog.querySelector('button');
    if (insertButton && insertButton.textContent?.includes('Insert')) {
      insertButton.textContent = this.translateService.instant('EDITOR.insert');
    }
  }
}
