import { NgStyle } from '@angular/common';
import { Component, effect, input, Input, Renderer2 } from '@angular/core';
import {
  AbstractControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';

import { UploadService } from '@core/services/utils/upload';
import { VideoService } from '@core/services/utils/video';
import { log } from '@core/utils/logger';
import {
  IFileUpload,
  ShowUploadListImage,
} from '@models/interfaces/file-upload';
import { TranslateModule } from '@ngx-translate/core';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzIconDirective } from 'ng-zorro-antd/icon';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import {
  NzUploadFile,
  NzUploadModule,
  NzUploadXHRArgs,
} from 'ng-zorro-antd/upload';
import { Observable, of } from 'rxjs';

enum ValidatorsField {
  VIDEOS = 'videos',
}

const VIDEO_LIMITS = {
  maxFileSize: 100 * 1024 * 1024, // 100MB
  supportedFormats: [
    'video/mp4',
    'video/mov',
    'video/avi',
    'video/wmv',
    'video/flv',
    'video/mpeg',
    'video/mpg',
    'video/m4v',
    'video/webm',
    'video/ogg',
    'video/3gp',
    'video/3g2',
    'video/mj2',
  ],
};

@Component({
  selector: 'app-input-video-upload',
  standalone: true,
  imports: [
    NzUploadModule,
    FormsModule,
    ReactiveFormsModule,
    NzModalModule,
    TranslateModule,
    NzFormModule,
    NzPopoverModule,
    NgStyle,
    NzIconDirective,
  ],
  providers: [UploadService, VideoService],
  templateUrl: './input-video-upload.component.html',
  styleUrl: './input-video-upload.component.less',
})
export class InputVideoUploadComponent {
  @Input() parentForm: FormGroup | any;
  @Input() controlName: string;
  @Input() label: string;
  @Input() disabled: boolean = false;
  @Input() maxTagCount: number;
  @Input() fileType: string;
  @Input() showAddButton: boolean = true;
  @Input() listType: 'text' | 'picture' | 'picture-card' = 'picture';
  @Input() videoContainerSize?: {
    width: string;
    height: string;
  };
  @Input() maxVideos: number = 1;
  @Input() customUploadRequest?: (item: NzUploadXHRArgs) => any;
  @Input() customRemoveHandler?: (file: NzUploadFile) => boolean;
  @Input() customUploadService?: UploadService;

  fileList = input<NzUploadFile[]>([]);
  videoList = input<IFileUpload[]>([]);

  protected fileListEffect = effect(() => {
    this.fileList();
    this.getActiveUploadService().FileList = this.fileList();
  });

  protected videoListEffect = effect(() => {
    this.videoList();
    this.getActiveUploadService().ImageList = this.videoList();
    console.log('Video list effect', this.videoList());
  });

  protected previewVideo: string | undefined = '';
  protected previewVisible = false;

  protected emptyVideoElement = '.ant-upload.ant-upload-select';
  protected listVideoElement = '.ant-upload-list-picture-container';

  constructor(
    private renderer2: Renderer2,
    private videoService: VideoService,
    private uploadService: UploadService,
  ) {}

  /**
   * Restituisce l'upload service attivo (personalizzato o di default)
   */
  private getActiveUploadService(): UploadService {
    return this.customUploadService || this.uploadService;
  }

  ngOnInit(): void {
    this.getActiveUploadService().FileList = this.fileList();
  }

  ngAfterViewInit(): void {
    const videoContainer = document.querySelector(this.emptyVideoElement);
    log('Video container', videoContainer);
    this.#setContainerSize(videoContainer);
    const listContainer = document.querySelector(this.listVideoElement);
    log('List container', listContainer);
    this.#setContainerSize(listContainer);
  }

  #setContainerSize(element: Element): void {
    if (
      this.videoContainerSize?.width &&
      this.videoContainerSize?.height &&
      element
    ) {
      this.renderer2.setStyle(element, 'width', this.videoContainerSize?.width);
      this.renderer2.setStyle(
        element,
        'height',
        this.videoContainerSize?.height,
      );
      this.renderer2.setStyle(element, 'margin-top', '-22px');
    }
  }

  handlePreview = async (file: NzUploadFile): Promise<void> => {
    if (file.response?.blobUrl) {
      this.previewVideo = file.response.blobUrl;
    } else if (file.thumbUrl) {
      this.previewVideo = file.thumbUrl;
    } else if (file.url) {
      this.previewVideo = file.url;
    }
    this.previewVisible = true;
  };

  isRequired() {
    return this.parentForm
      .get(this.controlName)
      .hasValidator(Validators.required);
  }

  public uploadRequest = (item: NzUploadXHRArgs) => {
    // Usa l'handler personalizzato se disponibile
    if (this.customUploadRequest) {
      return this.customUploadRequest(item);
    }

    // Altrimenti usa l'implementazione di default con fake upload
    return this.createFakeVideoUpload(item);
  };

  private createFakeVideoUpload(item: NzUploadXHRArgs) {
    const file = item.file;
    const totalSize = file.size || 1024 * 1024; // Default 1MB
    let uploaded = 0;

    // Simulate upload progress
    const progressInterval = setInterval(() => {
      uploaded += Math.random() * (totalSize * 0.1);

      if (uploaded >= totalSize) {
        uploaded = totalSize;
        clearInterval(progressInterval);

        // Convert file to blob
        let videoBlob: Blob;
        if (file instanceof File) {
          videoBlob = new Blob([file], { type: file.type });
        } else {
          videoBlob = new Blob([file as any], {
            type: file.type || 'video/mp4',
          });
        }

        const blobUrl = URL.createObjectURL(videoBlob);

        // Generate thumbnail from video
        this.generateVideoThumbnail(file, blobUrl)
          .then((thumbnailData) => {
            // Create fake response with thumbnail
            const fakeResponse = {
              ok: true,
              data: file,
              blob: videoBlob,
              blobUrl: blobUrl,
              thumbnail: thumbnailData.base64,
              thumbnailBlob: thumbnailData.blob,
              status: 'success',
            };

            // Update the file with response data
            const updatedFile: NzUploadFile = {
              ...file,
              status: 'done' as const,
              response: fakeResponse,
              thumbUrl: thumbnailData.base64, // Use thumbnail for thumbnail
              url: blobUrl, // Use blob URL for preview
            };

            // Update the upload manager file list
            const activeService = this.getActiveUploadService();
            activeService.FileList = activeService.FileList.map((f) =>
              f.uid === file.uid ? updatedFile : f,
            );

            // Update form value
            this.videosField.patchValue(activeService.FileList);

            // Call success callback
            item.onSuccess(fakeResponse, file, null);

            console.log('Video uploaded successfully with blob:', videoBlob);
            console.log('Blob URL created:', blobUrl);
            console.log(
              'Thumbnail generated:',
              thumbnailData.base64.substring(0, 50) + '...',
            );
          })
          .catch((error) => {
            console.error('Failed to generate thumbnail:', error);

            // Continue without thumbnail if generation fails
            const fakeResponse = {
              ok: true,
              data: file,
              blob: videoBlob,
              blobUrl: blobUrl,
              status: 'success',
            };

            const updatedFile: NzUploadFile = {
              ...file,
              status: 'done' as const,
              response: fakeResponse,
              thumbUrl: blobUrl, // Fallback to blob URL
              url: blobUrl,
            };

            // Update the upload manager file list
            const activeService = this.getActiveUploadService();
            activeService.FileList = activeService.FileList.map((f) =>
              f.uid === file.uid ? updatedFile : f,
            );

            // Update form value
            this.videosField.patchValue(activeService.FileList);

            // Call success callback
            item.onSuccess(fakeResponse, file, null);
          });
      } else {
        // Report progress
        const progress = Math.round((uploaded / totalSize) * 100);
        item.onProgress({ percent: progress }, file);
      }
    }, 100);

    // Return subscription
    return {
      unsubscribe: () => clearInterval(progressInterval),
    };
  }

  /**
   * Generates a thumbnail from a video file
   * @param file Video file
   * @param blobUrl Blob URL for the video
   * @returns Promise with thumbnail data
   */
  private generateVideoThumbnail(
    file: any,
    blobUrl: string,
  ): Promise<{ base64: string; blob: Blob }> {
    return new Promise((resolve, reject) => {
      // If it's not a File object, reject
      if (!(file instanceof File)) {
        reject(new Error('File is not a File object'));
        return;
      }

      const video = document.createElement('video');
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        reject(new Error('Canvas context not available'));
        return;
      }

      // Set canvas dimensions for thumbnail
      canvas.width = 200;
      canvas.height = 150;

      // Set video properties
      video.muted = true;
      video.playsInline = true;
      video.crossOrigin = 'anonymous';

      video.onloadedmetadata = () => {
        // Set video time to 1 second to capture a good frame
        video.currentTime = 1;
      };

      video.onseeked = () => {
        try {
          // Draw the video frame to canvas
          ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

          // Convert canvas to base64
          const base64 = canvas.toDataURL('image/jpeg', 0.8);

          // Convert to blob
          canvas.toBlob(
            (blob) => {
              if (blob) {
                resolve({ base64, blob });
              } else {
                reject(new Error('Failed to create blob'));
              }
            },
            'image/jpeg',
            0.8,
          );
        } catch (error) {
          reject(error);
        }
      };

      video.onerror = () => {
        reject(new Error('Failed to load video'));
      };

      // Load the video
      video.src = blobUrl;
      video.load();
    });
  }

  public setMediaUploadHeaders = () => {
    return {
      'Content-Type': 'multipart/form-data',
      Accept: 'application/json',
    };
  };

  public removeItem = (file: NzUploadFile): boolean => {
    // Usa l'handler personalizzato se disponibile
    if (this.customRemoveHandler) {
      return this.customRemoveHandler(file);
    }

    // Altrimenti usa l'implementazione di default
    const activeService = this.getActiveUploadService();
    activeService.removeFiles(file);
    this.videosField.patchValue(activeService.FileList);
    activeService.FileList.length <= 0 ||
    !activeService.FileList.every((video) => video.error === undefined)
      ? !!this.parentForm.get(this.controlName).valid
      : !this.parentForm.get(this.controlName).valid;
    return false;
  };

  public get FileList(): NzUploadFile[] {
    return this.getActiveUploadService().FileList;
  }

  public set FileList(list: NzUploadFile[]) {
    this.getActiveUploadService().FileList = list;
  }

  public get videosField(): AbstractControl {
    return this.parentForm.get(ValidatorsField.VIDEOS)!;
  }

  public get Limits() {
    return VIDEO_LIMITS;
  }

  public showUploadList: ShowUploadListImage = {
    showPreviewIcon: true,
    showRemoveIcon: true,
    hidePreviewIconInNonImage: false, // Show preview for videos
  };

  // Custom preview file handler for videos
  public previewFile = (file: NzUploadFile): Observable<string> => {
    // If file has a blob URL in response, use it
    if (file.response?.blobUrl) {
      return of(file.response.blobUrl);
    }

    // If file has a thumbUrl, use it
    if (file.thumbUrl) {
      return of(file.thumbUrl);
    }

    // If file has a url, use it
    if (file.url) {
      return of(file.url);
    }

    // Fallback to empty string
    return of('');
  };

  // Custom download handler for videos
  public downloadFile = (file: NzUploadFile): void => {
    // Try to get blob URL from response
    let downloadUrl = '';

    if (file.response?.blobUrl) {
      downloadUrl = file.response.blobUrl;
    } else if (file.thumbUrl) {
      downloadUrl = file.thumbUrl;
    } else if (file.url) {
      downloadUrl = file.url;
    }

    if (downloadUrl) {
      // Create a temporary anchor element to trigger download
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = file.name || 'video';
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } else {
      console.warn('No download URL available for file:', file);
    }
  };
}
