<app-table
  [tableLayoutSettings]="tableLayoutSettings()"
  [loading]="loading()"
  [data]="data()"
  [refresh]="refreshData()"
  (onRefreshTimerTick)="onQueryChange(tableQueryRequest()!)"
  [tableId]="'_awardsTable'"
  (onQueryChange)="onQueryChange($event)"
>
</app-table>

<ng-template #tplId let-element="element" let-row="row">
  <a
    ellipsis
    nz-typography
    [nzEllipsis]="element"
    nz-tooltip
    [nzTooltipTitle]="element | translate"
    (click)="goToDetail(element)"
    >{{ element }}
  </a>
</ng-template>

<ng-template #tplImage let-element="element" let-row="row">
  <nz-avatar-group class="d-inline pointer">
    @for (el of element; track $index) {
      <nz-avatar
        nz-tooltip
        [nzTooltipTitle]="el.name"
        [nzSrc]="el.url"
        [nzSize]="'small'"
        (click)="onImageClick(element)"
      ></nz-avatar>
    }
  </nz-avatar-group>
</ng-template>

<ng-template #tplAwards let-element="element" let-row="row">
  @for (el of element; track $index) {
    {{ el.product.name }}
    @if (!$last && element.length > 1) {
      -
    }
  }
</ng-template>
