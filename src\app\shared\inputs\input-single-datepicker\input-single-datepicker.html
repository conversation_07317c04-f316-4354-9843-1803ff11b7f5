<div class="container">
  <div class="w-100">
    <nz-form-item [formGroup]="parentForm()" [style]="getCombinedStyles()">
      @if (!!label()) {
        <nz-form-label [nzRequired]="isStartRequired()" nzNoColon="true">
          {{ label() | translate }}
        </nz-form-label>
      }

      <nz-form-control [nzErrorTip]="errorTpl">
        <nz-date-picker
          [nzDisabledDate]="disabledDates()"
          [nzFormat]="nzFormat()"
          [nzShowNow]="showNow()"
          [nzPlaceHolder]="placeholder() | translate"
          [nzAllowClear]="allowClear()"
          (nzOnOpenChange)="handleEndOpenChange($event)"
          [formControlName]="controlName()"
          [style]="{ width: '100%' }"
        ></nz-date-picker>

        <ng-template #errorTpl let-control>
          @if (control.touched && control.hasError("required")) {
            {{ "Campo obbligatorio" }}
          }
        </ng-template>
      </nz-form-control>
    </nz-form-item>
  </div>
</div>
