import { ComponentFixture, TestBed } from '@angular/core/testing';

import { InputVideoUploadComponent } from './input-video-upload.component';

describe('InputVideoUploadComponent', () => {
  let component: InputVideoUploadComponent;
  let fixture: ComponentFixture<InputVideoUploadComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [InputVideoUploadComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(InputVideoUploadComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
