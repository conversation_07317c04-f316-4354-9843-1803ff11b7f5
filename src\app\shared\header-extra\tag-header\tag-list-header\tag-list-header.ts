import { Component, inject } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { PopoverButtonComponent } from '@shared/buttons/popover-button/popover-button';
import { SimpleButtonComponent } from '@shared/buttons/simple-button/simple-button';
import { NzSpaceComponent, NzSpaceItemDirective } from 'ng-zorro-antd/space';

@Component({
  selector: 'app-tag-list-header',
  imports: [
    NzSpaceComponent,
    SimpleButtonComponent,
    NzSpaceItemDirective,
    PopoverButtonComponent,
    TranslateModule,
  ],
  templateUrl: './tag-list-header.html',
  styleUrl: './tag-list-header.less',
})
export class TagListHeader {
  private router = inject(Router);

  onAddTagClick() {
    this.router.navigateByUrl('/tags/create');
  }
}
