import { NgTemplateOutlet } from '@angular/common';
import { Component, computed, DestroyRef, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Router } from '@angular/router';
import { RolePermissionDirective } from '@core/directives/role-permission';
import { AdminService } from '@core/services/http/admin';
import { AuthService } from '@core/services/http/auth';
import { HeaderService } from '@core/services/utils/header';
import { MessageService } from '@core/services/utils/message';
import { ModalService } from '@core/services/utils/modal';
import { roleType } from '@models/enums/role';
import { TranslateModule } from '@ngx-translate/core';
import { ActionsButtonComponent } from '@shared/buttons/actions-button/actions-button';
import { PopoverButtonComponent } from '@shared/buttons/popover-button/popover-button';
import { ITableRowAction } from '@shared/table/types/table.action';
import { NzSpaceModule } from 'ng-zorro-antd/space';

@Component({
  selector: 'app-admin-detail-header',
  standalone: true,
  imports: [
    NgTemplateOutlet,
    PopoverButtonComponent,
    TranslateModule,
    NzSpaceModule,
    RolePermissionDirective,
    NgTemplateOutlet,
    ActionsButtonComponent,
  ],
  templateUrl: './admin-detail-header.html',
  styleUrl: './admin-detail-header.less',
})
export class AdminDetailHeader {
  // SERVICES
  private modalService = inject(ModalService);
  private adminService = inject(AdminService);
  private messageService = inject(MessageService);
  private router = inject(Router);
  private authService = inject(AuthService);
  private headerService = inject(HeaderService);
  private destroyRef = inject(DestroyRef);

  // PROPERTIES
  private _admin = this.adminService.admin$;
  protected tplMainButton = this.headerService.template;

  // COMPUTED
  public actions = computed<ITableRowAction[]>(() => {
    return this._admin() ? this.buildRowActions() : [];
  });

  // ENUMS
  roleType = roleType;

  /**
   * Costruisce le azioni disponibili per gli amministratori.
   */
  private buildRowActions(): ITableRowAction[] {
    return [
      {
        label: 'ADMIN.resendActivationAccount',
        icon: 'mail',
        callbackFn: () => {
          this.modalService.infoModal({
            confirmFn: () => {
              this.messageService.addLoadingMessage();
              this.authService
                .resendActivationAccount(this._admin().id)
                .pipe(takeUntilDestroyed(this.destroyRef))
                .subscribe(() => {
                  this.messageService.addSuccessMessage(
                    'ADMIN.resendActivationSuccess',
                  );
                });
            },
            title: 'ADMIN.activationAccountTitle',
            subtitle: 'ADMIN.activationAccountSubtitle',
          });
        },
        visibilityFn: () => this._admin().isEnabled && !this._admin().isActive,
      },
      {
        label: 'ACTIONS.enable',
        icon: 'check-circle',
        callbackFn: () => {
          this.modalService.infoModal({
            confirmFn: () => {
              this.messageService.addLoadingMessage();
              this.adminService
                .updateIsEnableStatus(this._admin().id, true)
                .pipe(takeUntilDestroyed(this.destroyRef))
                .subscribe(() => {
                  this.messageService.addSuccessMessage('ADMIN.updateSuccess');
                });
            },
            title: 'ADMIN.enableTitle',
            subtitle: 'ADMIN.enableSubtitle',
          });
        },
        visibilityFn: () =>
          !this._admin()?.isEnabled &&
          this._admin()?.role !== roleType.admin &&
          this._admin()?.isActive,
      },
      {
        label: 'ACTIONS.disable',
        icon: 'close-circle',
        callbackFn: () => {
          this.modalService.infoModal({
            confirmFn: () => {
              this.messageService.addLoadingMessage();
              this.adminService
                .updateIsEnableStatus(this._admin().id, true)
                .pipe(takeUntilDestroyed(this.destroyRef))
                .subscribe(() => {
                  this.messageService.addSuccessMessage('ADMIN.updateSuccess');
                });
            },
            title: 'ADMIN.disableTitle',
            subtitle: 'ADMIN.disableSubtitle',
          });
        },
        visibilityFn: () =>
          this._admin()?.isEnabled &&
          this._admin()?.role !== roleType.admin &&
          this._admin()?.isActive,
      },
      {
        label: 'ACTIONS.delete',
        icon: 'delete',
        visibilityFn: () => this._admin()?.role !== roleType.admin,
        callbackFn: () => {
          this.modalService.confirmDelete({
            confirmFn: () => {
              this.messageService.addLoadingMessage();
              this.adminService
                .delete(this._admin()?.id)
                .pipe(takeUntilDestroyed(this.destroyRef))
                .subscribe({
                  next: () => {
                    this.router.navigateByUrl('/admin');
                    this.messageService.addSuccessMessage(
                      'ADMIN.deleteSuccess',
                    );
                  },
                });
            },
            title: 'ADMIN.confirmDeleteTitle',
            subtitle: 'ADMIN.confirmDeleteSubtitle',
          });
        },
        danger: true,
      },
    ];
  }

  ngOnDestroy(): void {
    this.adminService.setAdmin(null);
  }
}
