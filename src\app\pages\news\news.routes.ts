import { Routes } from '@angular/router';
import { crudActionType } from '@models/enums/crud-action-type';
import { NewsCreateUpdate } from './news-create-update/news-create-update';
import { NewsList } from './news-list/news-list';

export const NEWS_ROUTES: Routes = [
  { path: '', component: NewsList },
  {
    path: 'create',
    component: NewsCreateUpdate,
    data: { crudType: crudActionType.create },
  },
  {
    path: ':id',
    component: NewsCreateUpdate,
    data: { crudType: crudActionType.update },
  },
];
