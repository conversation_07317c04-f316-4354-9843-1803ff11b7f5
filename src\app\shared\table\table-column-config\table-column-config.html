<div class="table-columns">
  <button
    nz-button
    nzSize="small"
    nz-dropdown
    [nzDropdownMenu]="menu_columns"
    [nzTrigger]="'click'"
    [(nzVisible)]="columnPopoverVisible"
    [nzPlacement]="'bottomRight'"
  >
    {{ "TABLE.columns" | translate
    }}<i
      nz-icon
      [nzType]="columnPopoverVisible ? 'up' : 'down'"
      nzTheme="outline"
    ></i>
  </button>

  <nz-dropdown-menu #menu_columns="nzDropdownMenu">
    <nz-list
      cdkDropList
      (cdkDropListDropped)="dropItem($event)"
      nzItemLayout="horizontal"
      [nzSize]="'small'"
      [nzBordered]="false"
      class="dropdown-column-list shadowing"
    >
      <nz-list-header class="column-dropdown-header">
        <div class="column-dropdown-title">
          {{ "TABLE.tableColumnSettingsDropdownTitle" | translate }}
        </div>
        <div class="column-dropdown-subtitle">
          {{ "TABLE.tableColumnSettingsDropdownSubTitle" | translate }}
        </div>
        <div nz-row class="column-dropdown-actions">
          <div nz-col nzSpan="12">
            <a (click)="resetColumns()" (keyup)="resetColumns()" tabindex="0">
              <i nz-icon nzType="reload" nzTheme="outline"></i>
              {{ "TABLE.reset" | translate }}
            </a>
          </div>
          <div nz-col nzSpan="12" style="text-align: right">
            @if (listOfColumns() | areAllColumnsHide) {
              <a
                (click)="setAllColumnsVisibility(true)"
                (keyup)="setAllColumnsVisibility(true)"
                tabindex="1"
              >
                <i nz-icon nzType="eye" nzTheme="outline"></i>
                {{ "TABLE.showAll" | translate }}
              </a>
            }
            @if (!(listOfColumns() | areAllColumnsHide)) {
              <a
                (click)="setAllColumnsVisibility(false)"
                (keyup)="setAllColumnsVisibility(false)"
                tabindex="2"
              >
                <i nz-icon nzType="eye-invisible" nzTheme="outline"></i>
                {{ "TABLE.hideAll" | translate }}
              </a>
            }
          </div>
        </div>
      </nz-list-header>
      <div class="column-list">
        @for (item of listOfColumns(); track trackByColumn($index, item)) {
          @if (!item.lockOnLeft && !item.lockOnRight) {
            <nz-list-item cdkDrag cdkDragLockAxis="y">
              <ng-container *ngTemplateOutlet="tplColListItem"></ng-container>
            </nz-list-item>
          }
          @if (item.lockOnLeft || item.lockOnRight) {
            <nz-list-item>
              <ng-container *ngTemplateOutlet="tplColListItem"></ng-container>
            </nz-list-item>
          }
          <ng-template #tplColListItem>
            <div nz-row style="width: 100%; font-size: 13px">
              <div nz-col [nzSpan]="3">
                @if (!item.lockOnLeft && !item.lockOnRight) {
                  <a class="drag-and-drop">
                    <i
                      nz-icon
                      nzType="client-ui:drag-drop"
                      nzTheme="outline"
                    ></i>
                  </a>
                }
                @if (item.lockOnLeft || item.lockOnRight) {
                  <i nz-icon nzType="lock" nzTheme="outline"></i>
                }
              </div>
              <div nz-col [nzSpan]="17">
                <div nz-row style="font-weight: 600">
                  {{ item.title | translate }}
                </div>
              </div>
              <div nz-col [nzSpan]="4" style="text-align: right">
                <nz-switch
                  nzSize="small"
                  [(ngModel)]="item.visible"
                  [nzDisabled]="item.lockOnLeft || item.lockOnRight"
                  (ngModelChange)="setDisplayedColumnNames()"
                ></nz-switch>
              </div>
            </div>
          </ng-template>
        }
      </div>
    </nz-list>
  </nz-dropdown-menu>
</div>
