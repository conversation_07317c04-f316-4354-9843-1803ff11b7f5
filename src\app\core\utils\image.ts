import { IFileSize } from '@models/interfaces/file-upload';
import { Observable } from 'rxjs';

export function getBase64Async(
  file: File,
): Promise<string | ArrayBuffer | null> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
}

export function getBase64(file: File): Observable<string> {
  return new Observable((observer) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = (event: any) => {
      observer.next(event.target.result);
      observer.complete();
    };
    reader.onerror = (event: any) => {
      //log('File could not be read: ' + event.target.error.code);
      observer.next(event.target.error.code);
      observer.complete();
    };
  });
}

export function getImageObject(
  base64: string,
): Observable<{ width: number; height: number; base64: string }> {
  const myobj = {
    height: 0,
    width: 0,
    base64: '',
  };
  return new Observable((observer) => {
    const img = new Image();
    img.src = base64;
    img.onload = (res: any) => {
      myobj.height = res.currentTarget['height'];
      myobj.width = res.currentTarget['width'];
      myobj.base64 = img.src;
      observer.next(myobj);
      observer.complete();
    };
    img.onerror = (event: any) => {
      //log('Image could not be read: ', + event);
      observer.next(event);
      observer.complete();
    };
  });
}

/** Calculate base64 size image */
export function calculateBase64ImageSize(base64: string): IFileSize {
  // Convert base64 string to binary data
  const binaryData = atob(base64);
  // Calculate the size of binary data in bytes
  const bytes = binaryData.length;
  // Conversions
  const kilobytes = bytes / 1024;
  const megabytes = kilobytes / 1024;

  return {
    kilobytes: kilobytes,
    megabytes: megabytes,
  };
}

/** Get only the base64 part */
export function extractBase64FromDataUrl(dataUrl: string): string | null {
  const match = dataUrl.match(
    /^data:([a-zA-Z0-9]+\/[a-zA-Z0-9-.+]+)?;base64,(.+)$/,
  );

  if (match && match.length === 3) {
    return match[2];
  }

  return null;
}
