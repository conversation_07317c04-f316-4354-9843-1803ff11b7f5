<!-- MAIN CONTAINER -->
<div class="container">
  <!-- CATEGORY FORM -->
  <form nz-form class="form" [formGroup]="baseForm" nzLayout="vertical">
    <!-- LOADING SPINNER -->
    <nz-spin [nzSpinning]="loading()">
      <!-- CATEGORY INFO SECTION -->
      <div nz-row class="w-100" [nzGutter]="16">
        <div nz-col [nzSpan]="12">
          <div nz-row [nzGutter]="16">
            <div nz-col [nzSpan]="24">
              <!-- CATEGORY NAME -->
              <app-input-generic
                [parentForm]="baseForm"
                [controlName]="'name'"
                [label]="'CATEGORIES.categoryName' | translate"
                [placeholder]="'CATEGORIES.categoryNamePlaceholder' | translate"
                [minLength]="3"
              ></app-input-generic>
            </div>
            <div nz-col [nzSpan]="24">
              <!-- CATEGORY DESCRIPTION -->
              <app-input-textarea
                [parentForm]="baseForm"
                [controlName]="'description'"
                [label]="'CATEGORIES.description' | translate"
                [placeholder]="'CATEGORIES.descriptionPlaceholder' | translate"
                [minLength]="10"
                [size]="{ minRows: 4.6, maxRows: 15 }"
              ></app-input-textarea>
            </div>
          </div>
        </div>
        <div nz-col [nzSpan]="12">
          <!-- CATEGORY IMAGE UPLOAD -->
          <app-input-image-upload
            [parentForm]="baseForm"
            [controlName]="'images'"
            [label]="'image'"
            [fileType]="'image/png,image/jpeg'"
            [showAddButton]="FileList?.length === 0"
            [imageContainerSize]="{ width: '200px', height: '200px' }"
            [maxImages]="1"
            [fileList]="FileList || []"
            [imageList]="ImageList || []"
          ></app-input-image-upload>
        </div>
      </div>
    </nz-spin>
  </form>
</div>

<!-- ACTION BUTTONS TEMPLATE -->
<ng-template #tplButton>
  <!-- SAVE/UPDATE BUTTON -->
  <button
    nz-button
    nzType="primary"
    (click)="onDataSaveClick()"
    [disabled]="!isValidForm() || loading()"
  >
    {{ saveButtonTitle() | translate }}
    @if (crudMode() === crudActionType.create) {
      <i nz-icon nzType="client-ui:plus"></i>
    } @else {
      <i nz-icon nzType="save"></i>
    }
  </button>
</ng-template>
