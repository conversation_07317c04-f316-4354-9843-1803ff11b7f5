import { NgTemplateOutlet } from '@angular/common';
import { Component, computed, DestroyRef, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Router } from '@angular/router';
import { RolePermissionDirective } from '@core/directives/role-permission';
import { AwardsService } from '@core/services/http/awards';
import { HeaderService } from '@core/services/utils/header';
import { MessageService } from '@core/services/utils/message';
import { ModalService } from '@core/services/utils/modal';
import { roleType } from '@models/enums/role';
import { TranslateModule } from '@ngx-translate/core';
import { ActionsButtonComponent } from '@shared/buttons/actions-button/actions-button';
import { PopoverButtonComponent } from '@shared/buttons/popover-button/popover-button';
import { ITableRowAction } from '@shared/table/types/table.action';
import { NzSpaceModule } from 'ng-zorro-antd/space';

@Component({
  selector: 'app-awards-detail-header',
  imports: [
    NgTemplateOutlet,
    PopoverButtonComponent,
    TranslateModule,
    NzSpaceModule,
    RolePermissionDirective,
    NgTemplateOutlet,
    ActionsButtonComponent,
  ],
  templateUrl: './awards-detail-header.html',
  styleUrl: './awards-detail-header.less',
})
export class AwardsDetailHeader {
  // SERVICES
  private headerService = inject(HeaderService);
  private awardService = inject(AwardsService);
  private modalService = inject(ModalService);
  private messageService = inject(MessageService);
  private router = inject(Router);
  private destroyRef = inject(DestroyRef);
  // PROPERTIES
  private _award = this.awardService.award$;
  public actions = computed<ITableRowAction[]>(() => {
    return this._award() ? this.buildRowActions() : ([] as ITableRowAction[]);
  });

  protected tplMainButton = this.headerService.template;

  // ENUMS
  roleType = roleType;

  private buildRowActions(): ITableRowAction[] {
    return [
      {
        label: 'ACTIONS.delete',
        icon: 'delete',
        visibilityFn: () => true,
        callbackFn: () => {
          this.modalService.confirmDelete({
            confirmFn: () => {
              this.messageService.addLoadingMessage();
              this.awardService
                .delete(this._award()?.id)
                .pipe(takeUntilDestroyed(this.destroyRef))
                .subscribe({
                  next: () => {
                    this.router.navigateByUrl('/awards');
                    this.messageService.addSuccessMessage(
                      'AWARDS.deleteSuccess',
                    );
                  },
                });
            },
            title: 'AWARDS.confirmDeleteTitle',
            subtitle: 'AWARDS.confirmDeleteSubtitle',
          });
        },
        danger: true,
      },
    ];
  }
}
