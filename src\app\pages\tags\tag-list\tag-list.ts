import {
  Component,
  inject,
  signal,
  TemplateRef,
  viewChild,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { currentSectionType } from '@models/enums/current-section';
import { ICategory } from '@models/interfaces/category';
import { TranslateModule } from '@ngx-translate/core';
import { ITableRowAction } from '@shared/table/types/table.action';
import { ITableColumn } from '@shared/table/types/table.column';
import { ITableQuery } from '@shared/table/types/table.query';
import { IRefreshData } from '@shared/table/types/table.refresh-data';
import { ITableSetting } from '@shared/table/types/table.settings';

import { trackEvent } from '@aptabase/web';
import { EllipsisDirective } from '@core/directives/ellipsis';
import { NullValuePipe } from '@core/pipes/null-value.pipe';
import { TagsService } from '@core/services/http/tags';
import { HeaderService } from '@core/services/utils/header';
import { MessageService } from '@core/services/utils/message';
import { ModalService } from '@core/services/utils/modal';
import { TableComponent } from '@shared/table/table';
import { TableQueryService } from '@shared/table/table-query.service';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzTypographyModule } from 'ng-zorro-antd/typography';

@Component({
  selector: 'app-tag-list',
  standalone: true,
  imports: [
    TableComponent,
    NullValuePipe,
    EllipsisDirective,
    NzToolTipModule,
    TranslateModule,
    NzTypographyModule,
  ],
  providers: [TableQueryService],
  templateUrl: './tag-list.html',
  styleUrl: './tag-list.less',
})
export class TagListComponent {
  // SERVICES
  private headerService = inject(HeaderService);
  private tagService = inject(TagsService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private modalService = inject(ModalService);
  private messageService = inject(MessageService);

  tplId = viewChild<TemplateRef<any>>('tplId');
  tplName = viewChild<TemplateRef<any>>('tplName');
  tplCount = viewChild<TemplateRef<any>>('tplCount');
  // Convert to signals
  protected loading = signal<boolean>(false);
  protected data = signal<any[]>([]);
  protected dataQuery = signal<any>(null);
  protected tableLayoutSettings = signal<ITableSetting>(<ITableSetting>{});
  protected tableQueryRequest = signal<ITableQuery | null>(null);
  protected refreshData = signal<IRefreshData>({
    interval: 0,
    min: 0,
    max: 180,
    step: 30,
  });

  constructor() {
    trackEvent('tag_list_page');
    const settings: ITableSetting = {
      singleRowActions: this.buildRowActions(),
      listOfColumns: this.buildTableColumns(),
      dynamicTableHeightOffset: 375,
      pagination: {
        total: 0,
        pageSize: 50,
        pageIndex: 1,
      },
    };
    this.tableLayoutSettings.set(settings);
    this.headerService.setCurrentSection(currentSectionType.tagList);
  }

  onQueryChange(query: ITableQuery) {
    this.tableQueryRequest.set(query);
    this.loading.set(true);

    this.tagService.search(query.meta, query.filter).subscribe((value) => {
      this.data.set(value.data);

      const currentSettings = this.tableLayoutSettings();
      currentSettings.pagination.total = value.meta!.page.total;
      currentSettings.pagination.pageIndex = value.meta!.page.pageIndex;
      currentSettings.pagination.pageSize = value.meta!.page.pageSize;
      this.tableLayoutSettings.set({ ...currentSettings });
      this.loading.set(false);
    });
  }

  ngAfterViewInit(): void {
    const currentSettings = this.tableLayoutSettings();
    currentSettings.listOfColumns.forEach(
      (col) =>
        (col.cellTemplate = col.cellTemplateName
          ? this[col.cellTemplateName]()
          : null),
    );
    this.tableLayoutSettings.set({ ...currentSettings });
  }

  private buildRowActions(): ITableRowAction[] {
    return [
      {
        label: 'ACTIONS.modify',
        icon: 'edit',
        callbackFn: (row: ICategory) => {
          this.router.navigate([row.id], { relativeTo: this.route });
        },
      },
      {
        label: 'ACTIONS.delete',
        icon: 'delete',
        callbackFn: (row: ICategory) => {
          this.modalService.confirmDelete({
            confirmFn: () => {
              this.messageService.addLoadingMessage();
              this.tagService.delete(row.id).subscribe({
                next: () => {
                  this.onQueryChange(this.tableQueryRequest()!);
                  this.messageService.addSuccessMessage('TAGS.deleteSuccess');
                },
              });
            },
            title: 'TAGS.confirmDeleteTitle',
            subtitle: 'TAGS.confirmDeleteSubtitle',
          });
        },
        danger: true,
      },
    ];
  }

  private buildTableColumns(): ITableColumn[] {
    return [
      {
        id: 'id',
        cellTemplate: this.tplId(),
        cellTemplateName: 'tplId',
        title: 'ID',
        sortOrder: null,
        sortFn: true,
        width: '50px',
        lockOnLeft: true,
        visible: true,
      },
      {
        id: 'name',
        cellTemplate: this.tplName(),
        cellTemplateName: 'tplName',
        title: 'TAGS.name',
        width: '75px',
        visible: true,
      },
      {
        id: 'count',
        cellTemplate: this.tplCount(),
        cellTemplateName: 'tplCount',
        title: 'TAGS.count',
        sortOrder: null,
        sortFn: true,
        width: '50px',
        visible: true,
      },
    ];
  }

  goToDetail(tagId: string) {
    this.router.navigate([tagId], { relativeTo: this.route });
  }
}
