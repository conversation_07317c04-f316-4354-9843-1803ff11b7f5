  html.dark {
    .NgxEditor__MenuBar {
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;
      border-bottom: 1px solid @text-editor-border-color;
      background-color: @dark;
      color: @white;

      .NgxEditor__MenuItem {
        color: @white;


        &:hover {
          background-color: rgba(255, 255, 255, 0.1);
        }

        &.NgxEditor__MenuItem--Active {
          background-color: rgba(255, 255, 255, 0.2);
          color: @white;
        }
      }

      .NgxEditor__Dropdown {
        background-color: @dark !important;
        color: @white !important;
        border: 1px solid @text-editor-border-color !important;

        .NgxEditor__DropdownItem {
          color: @white !important;
          background-color: @dark !important;

          &:hover {
            background-color: rgba(255, 255, 255, 0.1) !important;
          }
        }
      }
    }

    // Fix specifico per dropdown heading
    .NgxEditor__Dropdown--Heading {
      background-color: @dark !important;
      color: @white !important;
      border: 1px solid @text-editor-border-color !important;

      .NgxEditor__DropdownItem {
        color: @white !important;
        background-color: @dark !important;

        &:hover {
          background-color: rgba(255, 255, 255, 0.1) !important;
        }
      }
    }

    // Fix per tutte le dropdown del text editor
    .NgxEditor__MenuBar .NgxEditor__Dropdown {
      background-color: @dark !important;
      color: @white !important;

      .NgxEditor__DropdownItem {
        color: @white !important;
        background-color: @dark !important;
      }
    }

    .NgxEditor__MenuItem.NgxEditor__MenuItem--IconContainer {
      .NgxEditor__Popup {
        background: @dark;
      }
    }

    .NgxEditor {
      border-top-left-radius: 0;
      border-top-right-radius: 0;
      border: none;
      background-color: @dark;
      color: @white;

      .ProseMirror {
        background-color: @dark;
        color: @white;
        min-height: calc(10 * 1.5em); // 10 righe con line-height di 1.5em
        padding: 8px 12px;
        overflow: auto; // Gestisce il contenuto che potrebbe uscire
        word-wrap: break-word; // Forza il wrap delle parole lunghe

        &:focus {
          outline: none;
          border: none;
        }

        p {
          color: @white;
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
          color: @white;
        }

        strong {
          color: @white;
        }

        em {
          color: @white;
        }

        ul,
        ol {
          color: @white;
          margin-left: 0;
          padding-left: 1.5em;

          li {
            color: @white;
            margin: 0;
            padding: 0;
            list-style-position: inside;
          }
        }

        blockquote {
          color: @white;
          border-left: 3px solid @medium_grey;
        }

        code {
          background-color: rgba(255, 255, 255, 0.1);
          color: @white;
        }
      }
    }

    .CodeMirror {
      border: 1px solid #363636;
      height: auto;
      margin-bottom: 0.7rem;
      background-color: @dark;
      color: @white;

      pre {
        white-space: pre !important;
        color: @white;
      }

      .CodeMirror-cursor {
        border-left: 1px solid @white;
      }

      .CodeMirror-selected {
        background-color: rgba(255, 255, 255, 0.2);
      }
    }
  }

  // Stili globali per tutte le dropdown del ngx-editor in dark mode
  .NgxEditor__Dropdown,
  .NgxEditor__Dropdown--Heading,
  [class*="NgxEditor__Dropdown"] {
    background-color: @dark !important;
    color: @white !important;
    border: 1px solid @text-editor-border-color !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5) !important;

    .NgxEditor__DropdownItem,
    [class*="NgxEditor__DropdownItem"] {
      color: @white !important;
      background-color: @dark !important;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1) !important;
        color: @white !important;
      }

      &:focus {
        background-color: rgba(255, 255, 255, 0.1) !important;
        color: @white !important;
      }
    }
  }

  // Override aggressivo per tutte le possibili dropdown del ngx-editor
  .NgxEditor__Dropdown,
  .NgxEditor__Dropdown--Heading,
  div[class*="NgxEditor__Dropdown"],
  .ngx-editor .NgxEditor__Dropdown {
    background: #141414 !important;
    background-color: #141414 !important;
    color: #ffffff !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;

    // Forza tutti gli elementi figli
    * {
      color: #ffffff !important;
      background-color: #141414 !important;
    }

    .NgxEditor__DropdownItem,
    div[class*="NgxEditor__DropdownItem"],
    button[class*="NgxEditor__DropdownItem"],
    .ngx-editor .NgxEditor__DropdownItem {
      color: #ffffff !important;
      background: #141414 !important;
      background-color: #141414 !important;

      &:hover,
      &:focus,
      &:active {
        background: rgba(255, 255, 255, 0.1) !important;
        background-color: rgba(255, 255, 255, 0.1) !important;
        color: #ffffff !important;
      }
    }
  }

  // Stili CSS puri per forzare il dark mode sulle dropdown
  // html.dark .NgxEditor__Dropdown,
  // html.dark .NgxEditor__Dropdown--Heading,
  // html.dark div[class*="NgxEditor__Dropdown"] {
  //   background: #141414 !important;
  //   background-color: #141414 !important;
  //   color: #ffffff !important;
  //   border: 1px solid rgba(255, 255, 255, 0.2) !important;
  // }

  // html.dark .NgxEditor__DropdownItem,
  // html.dark div[class*="NgxEditor__DropdownItem"],
  // html.dark button[class*="NgxEditor__DropdownItem"] {
  //   color: #ffffff !important;
  //   background: #141414 !important;
  //   background-color: #141414 !important;
  // }

  // html.dark .NgxEditor__DropdownItem:hover,
  // html.dark div[class*="NgxEditor__DropdownItem"]:hover,
  // html.dark button[class*="NgxEditor__DropdownItem"]:hover {
  //   background: rgba(255, 255, 255, 0.1) !important;
  //   background-color: rgba(255, 255, 255, 0.1) !important;
  //   color: #ffffff !important;
  // }