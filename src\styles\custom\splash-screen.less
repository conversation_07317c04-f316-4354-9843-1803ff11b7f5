/* SPLASH SCREEN STATICO -------------------------------------- */
#app-splash {
  position: fixed;
  inset: 0;
  z-index: 9999;
  width: 100dvw;
  height: 100dvh;
  display: grid;
  place-content: center;
  background-color: #f9f9f9;
  /* usa variabile di tema */
  overflow: hidden;


  img {
    transform: scale(1);
    /* riattiva se vuoi: animation: pulse 1s infinite; */
    transition: opacity .2s ease-in;
  }

  /* uscita dopo bootstrap */
  &.fade-out {
    animation: overlayFade .5s forwards;

    img {
      animation: pulse-final .5s forwards;
    }
  }
}

/* 3) BACKDROP OPZIONALE ----------------------------------------- */
.fixed-bg {
  position: fixed;
  width: 100vw;
  height: 100vh;
  top: 0;
  left: 0;
  background-color: transparent;
  opacity: .55;
  z-index: 9999;
}

/* 4) KEYFRAMES  --------------- */
@keyframes pulse {
  0% {
    transform: scale(.85);
  }

  50% {
    transform: scale(1);
  }

  80% {
    transform: scale(.85);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes pulse-final {
  0% {
    transform: scale(1);
  }

  70% {
    transform: scale(4.5);
  }

  100% {
    transform: scale(9);
    opacity: 0;
  }
}

@keyframes overlayFade {
  to {
    opacity: 0;
  }
}