import { Component, inject, OnInit } from '@angular/core';
import { ActivatedRoute, RouterLink, RouterOutlet } from '@angular/router';
import { CategoriesService } from '@core/services/http/categories';
import { TranslateModule } from '@ngx-translate/core';
import { NzIconDirective } from 'ng-zorro-antd/icon';
import { NzTabsModule } from 'ng-zorro-antd/tabs';

@Component({
  selector: 'app-tag',
  standalone: true,
  imports: [
    RouterOutlet,
    TranslateModule,
    NzIconDirective,
    RouterLink,
    NzTabsModule,
  ],
  templateUrl: './tag.html',
  styleUrl: './tag.less',
})
export class TagComponent implements OnInit {
  id: string;
  private categoryService = inject(CategoriesService);

  constructor(private route: ActivatedRoute) {}

  ngOnInit() {
    // Get the id parameter from the route
    this.route.params.subscribe((params) => {
      this.id = params['id'];
      this.categoryService.setCategoryId(this.id);
    });
  }
}
