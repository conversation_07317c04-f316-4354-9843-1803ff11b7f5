<div class="container">
  <div class="pr-16 w-100">
    <nz-form-item [formGroup]="parentForm()">
      @if (!!labelStart) {
        <nz-form-label [nzRequired]="isStartRequired()" nzNoColon="true">
          {{ labelStart }}
        </nz-form-label>
      }

      <nz-form-control [nzErrorTip]="errorTpl">
        <nz-date-picker
          #startDatePicker
          [nzDisabledDate]="disabledEndDate"
          [nzShowTime]="{
            nzFormat: 'HH:mm',
          }"
          nzFormat="yyyy-MM-dd HH:mm"
          [(ngModel)]="startValue"
          [nzPlaceHolder]="placeholderStart()"
          (nzOnOpenChange)="handleEndOpenChange($event)"
          [formControlName]="controlNameStart()"
        ></nz-date-picker>

        <ng-template #errorTpl let-control>
          @if (control.touched && control.hasError("required")) {
            {{ "Campo obbligatorio" }}
          }
        </ng-template>
      </nz-form-control>
    </nz-form-item>
  </div>
  <div class="w-100 second-date">
    <nz-form-item [formGroup]="parentForm()">
      @if (!!labelEnd) {
        <nz-form-label [nzRequired]="isEndRequired()" nzNoColon="true">
          {{ labelEnd }}
        </nz-form-label>
      }

      <nz-form-control [nzErrorTip]="errorTpl">
        <nz-date-picker
          #endDatePicker
          [nzDisabledDate]="disabledEndDate"
          [nzShowTime]="{
            nzFormat: 'HH:mm',
          }"
          nzFormat="yyyy-MM-dd HH:mm"
          [(ngModel)]="endValue"
          [nzPlaceHolder]="placeholderEnd()"
          (nzOnOpenChange)="handleEndOpenChange($event)"
          [formControlName]="controlNameEnd()"
        ></nz-date-picker>

        <ng-template #errorTpl let-control>
          @if (control.touched && control.hasError("required")) {
            {{ "Campo obbligatorio" }}
          }
        </ng-template>
      </nz-form-control>
    </nz-form-item>
  </div>
</div>
