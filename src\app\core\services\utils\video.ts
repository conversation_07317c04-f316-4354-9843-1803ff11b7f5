import {
  HttpClient,
  HttpErrorResponse,
  HttpRequest,
  HttpResponse,
} from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { log } from '@core/utils/logger';
import { FileResponse } from '@models/interfaces/file-upload';
import { NzUploadXHRArgs } from 'ng-zorro-antd/upload';
import { BehaviorSubject, Observable, tap } from 'rxjs';
import { MessageService } from './message';

/**
 * Servizio per la gestione delle immagini e upload di file.
 * Fornisce metodi per caricare immagini e gestire errori di upload.
 */
@Injectable({
  providedIn: 'root',
})
export class VideoService {
  private http = inject(HttpClient);
  private messageService = inject(MessageService);

  public uploadOnError$ = new BehaviorSubject<any>(null);

  /**
   * Demonstrates video upload with progress tracking and blob conversion
   */
  uploadVideoWithProgress(
    file: File,
  ): Observable<{ progress: number; blob?: Blob; blobUrl?: string }> {
    return new Observable((observer) => {
      const totalSize = file.size;
      let uploaded = 0;

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        uploaded += Math.random() * (totalSize * 0.1);

        if (uploaded >= totalSize) {
          uploaded = totalSize;
          clearInterval(progressInterval);

          // Convert to blob and create URL
          const blob = this.convertFileToBlob(file);
          const blobUrl = this.createBlobUrl(blob);

          observer.next({ progress: 100, blob, blobUrl });
          observer.complete();
        } else {
          const progress = Math.round((uploaded / totalSize) * 100);
          observer.next({ progress });
        }
      }, 100);

      // Cleanup on unsubscribe
      return () => {
        clearInterval(progressInterval);
      };
    });
  }

  private convertFileToBlob(file: File): Blob {
    return new Blob([file], { type: file.type });
  }

  private createBlobUrl(blob: Blob): string {
    return URL.createObjectURL(blob);
  }

  uploadFileLocal(item: NzUploadXHRArgs): Observable<NzUploadXHRArgs> {
    const formData = new FormData();

    console.log('ITEM FILE:', item.file);
    console.log('ITEM POST FILE: ', item.postFile);

    formData.append('file', <any>item.file);
    formData.append('postFile', <any>item.postFile);
    console.log('ITEM ACTION:', item.action);

    const req = new HttpRequest('POST', item?.action, formData, {
      reportProgress: true,
      withCredentials: false,
    });

    return new Observable((observer) => {
      this.http
        .request(req)
        .pipe(
          tap((event: HttpResponse<FileResponse>) => {
            console.log('EVENT: ', event);
            observer.next(item);
            observer.complete();
          }),
        )
        .subscribe({
          next: (event: HttpResponse<FileResponse>) => {
            item.onSuccess(event.body, item.file, event);
          },
          error: (err: HttpErrorResponse) => {
            log('ITEM ON ERROR: ', err);
            this.uploadOnError$.next(item);
            this.messageService.addErrorMessage(err.error.limits);
            item.onError('File bigger than 400 x 508 resolution', item.file);
          },
        });
    });
  }

  /**
   * Generates a thumbnail from a video file using HTML5 Canvas
   * @param file Video file to generate thumbnail from
   * @param timeInSeconds Time in seconds to capture frame (default: 1 second)
   * @returns Promise with thumbnail as base64 string
   */
  generateVideoThumbnail(
    file: File,
    timeInSeconds: number = 1,
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      const video = document.createElement('video');
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        reject(new Error('Canvas context not available'));
        return;
      }

      // Set canvas dimensions (you can adjust these)
      canvas.width = 200;
      canvas.height = 150;

      // Create object URL for the video
      const videoUrl = URL.createObjectURL(file);

      video.onloadedmetadata = () => {
        // Set video time to capture frame
        video.currentTime = timeInSeconds;
      };

      video.onseeked = () => {
        try {
          // Draw the video frame to canvas
          ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

          // Convert canvas to base64
          const thumbnail = canvas.toDataURL('image/jpeg', 0.8);

          // Clean up
          URL.revokeObjectURL(videoUrl);

          resolve(thumbnail);
        } catch (error) {
          URL.revokeObjectURL(videoUrl);
          reject(error);
        }
      };

      video.onerror = () => {
        URL.revokeObjectURL(videoUrl);
        reject(new Error('Failed to load video'));
      };

      // Load the video
      video.src = videoUrl;
      video.load();
    });
  }

  /**
   * Generates multiple thumbnails at different time points
   * @param file Video file to generate thumbnails from
   * @param timePoints Array of time points in seconds (default: [1, 5, 10])
   * @returns Promise with array of thumbnails
   */
  generateMultipleThumbnails(
    file: File,
    timePoints: number[] = [1, 5, 10],
  ): Promise<string[]> {
    const thumbnailPromises = timePoints.map((time) =>
      this.generateVideoThumbnail(file, time),
    );

    return Promise.all(thumbnailPromises);
  }

  /**
   * Generates a thumbnail and returns both base64 and blob
   * @param file Video file to generate thumbnail from
   * @returns Promise with thumbnail data
   */
  generateThumbnailWithBlob(
    file: File,
  ): Promise<{ base64: string; blob: Blob }> {
    return new Promise((resolve, reject) => {
      const video = document.createElement('video');
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        reject(new Error('Canvas context not available'));
        return;
      }

      canvas.width = 200;
      canvas.height = 150;

      const videoUrl = URL.createObjectURL(file);

      video.onloadedmetadata = () => {
        video.currentTime = 1;
      };

      video.onseeked = () => {
        try {
          ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

          // Get base64
          const base64 = canvas.toDataURL('image/jpeg', 0.8);

          // Convert to blob
          canvas.toBlob(
            (blob) => {
              if (blob) {
                URL.revokeObjectURL(videoUrl);
                resolve({ base64, blob });
              } else {
                URL.revokeObjectURL(videoUrl);
                reject(new Error('Failed to create blob'));
              }
            },
            'image/jpeg',
            0.8,
          );
        } catch (error) {
          URL.revokeObjectURL(videoUrl);
          reject(error);
        }
      };

      video.onerror = () => {
        URL.revokeObjectURL(videoUrl);
        reject(new Error('Failed to load video'));
      };

      video.src = videoUrl;
      video.load();
    });
  }
}
