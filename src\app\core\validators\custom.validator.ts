import {
  AbstractControl,
  AsyncValidatorFn,
  FormArray,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
} from '@angular/forms';
import { map, Observable, switchMap, timer } from 'rxjs';

/**
 * Classe di validatori custom per Reactive Forms Angular.
 * Contiene metodi statici per validazioni comuni e personalizzate su form e campi.
 */
export class CustomValidators {
  /**
   * Valida che due campi abbiano lo stesso valore (es. password e conferma password).
   * @param control1 Nome del primo controllo
   * @param control2 Nome del secondo controllo
   * @returns ValidatorFn che segnala errore se i valori non coincidono
   */
  public static confirmPasswordValidator(
    control1: string,
    control2: string,
  ): ValidatorFn {
    return (form: FormGroup): ValidationErrors | null => {
      if (form.get(control1)!.value === form.get(control2)!.value) {
        return null;
      } else {
        form.get(control2)!.setErrors({ passwordMismatch: true });
        return {
          passwordMismatch: true,
        };
      }
    };
  }

  /**
   * Valida che la data inserita sia successiva al 01/01/1900.
   * @returns ValidatorFn che segnala errore se la data non è valida
   */
  public static dateValidator(): ValidatorFn {
    return (control: AbstractControl) => {
      const startDate = new Date(1900, 0, 1);
      const value = new Date(control.value);
      const differenceMs = value.getTime() - startDate.getTime();

      if (differenceMs >= 0) {
        return null;
      } else return { DateNotValid: true };
    };
  }

  /**
   * Valida che l'intervallo orario sia corretto (end > start).
   * @param startHour Nome del controllo ora di inizio
   * @param endHour Nome del controllo ora di fine
   * @returns ValidatorFn che segnala errore se l'intervallo non è valido
   */
  public static timeRangeValidator(
    startHour: string,
    endHour: string,
  ): ValidatorFn {
    return (controls: AbstractControl) => {
      const startHourValue = controls.get(startHour).value;
      const endHourValue = controls.get(endHour).value;

      let dateA = new Date(startHourValue);
      let dateB = new Date(endHourValue);

      if (!dateA && !dateB) return { dateError: true };

      if (dateB > dateA) {
        controls.get(endHour).setErrors(null);
        return null;
      } else {
        controls.get(endHour).setErrors({ dateError: true });
        return { dateError: true };
      }
    };
  }

  /**
   * Valida che due intervalli temporali non si sovrappongano.
   * @param formArray FormArray contenente almeno due FormGroup con start/end
   * @returns ValidationErrors se sovrapposti, null altrimenti
   */
  public static nonOverlappingIntervalsValidator(
    formArray: AbstractControl,
  ): ValidationErrors | null {
    // Prima di tutto, controlliamo se è davvero un FormArray
    if (!(formArray instanceof FormArray)) {
      return null;
    }

    // Se non ho almeno 2 fasce, niente da validare
    if (formArray.length < 2) {
      return null;
    }

    // Ricavo i due FormGroup (assumendo di averne esattamente 2)
    const firstTimeSlot = formArray.at(0) as FormGroup;
    const secondTimeSlot = formArray.at(1) as FormGroup;

    // Estraggo i valori start e end
    const firstEnd = firstTimeSlot.get('end')?.value; // Date
    const secondStart = secondTimeSlot.get('start')?.value; // Date

    // Se il secondo inizia prima (o uguale) di quando finisce il primo => ERRORE
    if (firstEnd && secondStart && secondStart <= firstEnd) {
      // Ritorno un oggetto che rappresenta l'errore
      return { overlappingError: true };
    }
    // Altrimenti va tutto bene
    return null;
  }

  /**
   * Valida in modo asincrono un input tramite chiamata HTTP.
   * @param requestHttp Funzione che effettua la richiesta HTTP
   * @param errorName Nome dell'errore da restituire se non valido
   * @returns AsyncValidatorFn che segnala errore se il valore non è disponibile
   */
  public static validateInputAsync(
    requestHttp: (value: string) => Observable<any>,
    errorName: string,
  ): AsyncValidatorFn {
    return (control: AbstractControl): Observable<ValidationErrors | null> => {
      return timer(500).pipe(
        switchMap(() =>
          requestHttp(control.value).pipe(
            map((res) => (res.data?.available ? null : { [errorName]: true })),
          ),
        ),
      );
    };
  }

  public static emailRegex =
    '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$';

  public static anyInterNumberRegex = '^-?[0-9]\\d*(\\.\\d{1,2})?$';

  public static positiveInterNumberRegex = '/^[0-9]d*$/';

  public static subdomainRegex = '^[a-zA-Z0-9-]+$';

  public static urlRegex = /^https?:\/\/.+/;

  public static urlRegexAsString = '^https?://.*$';
}
