import { Routes } from '@angular/router';
import { crudActionType } from '@models/enums/crud-action-type';
import { EventsCreateUpdate } from './events-create-update/events-create-update';
import { EventsList } from './events-list/events-list';

export const EVENTS_ROUTES: Routes = [
  { path: '', component: EventsList },
  {
    path: 'create',
    component: EventsCreateUpdate,
    data: { crudType: crudActionType.create },
  },
  {
    path: ':id',
    component: EventsCreateUpdate,
    data: { crudType: crudActionType.update },
  },
];
