import { isDevMode } from '@angular/core';
import { environment } from '@env/environment';

const record: Record<string, boolean> = {};
export const PREFIX = '[Client]:';

// Log should only be printed in dev mode.
export const log = (...args: any[]): void => {
  if (!environment.production) {
    // eslint-disable-next-line
    console.log(PREFIX, ...args);
  }
};

// Warning should only be printed in dev mode and only once.
export const warn = (...args: any[]): void =>
  consoleCommonBehavior(
    (...arg: any[]) => console.warn(PREFIX, ...arg),
    ...args,
  );

function consoleCommonBehavior(
  consoleFunc: (...args: any) => void,
  ...args: any[]
): void {
  // if (environment.production || (isDevMode() && notRecorded(...args))) {
  if (isDevMode() && notRecorded(...args)) {
    consoleFunc(...args);
  }
}

function notRecorded(...args: any[]): boolean {
  const asRecord = args.reduce((acc, c) => acc + c.toString(), '');

  if (record[asRecord]) {
    return false;
  } else {
    record[asRecord] = true;
    return true;
  }
}
