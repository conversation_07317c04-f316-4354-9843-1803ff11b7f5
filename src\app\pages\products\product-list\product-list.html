<app-table
  [tableLayoutSettings]="tableLayoutSettings()"
  [loading]="loading()"
  [data]="data()"
  [refresh]="refreshData()"
  (onRefreshTimerTick)="onQueryChange(tableQueryRequest()!)"
  [tableId]="'_productsTable'"
  (onQueryChange)="onQueryChange($event)"
>
</app-table>

<ng-template #tplId let-element="element" let-row="row">
  <a
    ellipsis
    nz-typography
    [nzEllipsis]="element"
    nz-tooltip
    [nzTooltipTitle]="element | translate"
    (click)="goToDetail(element)"
    >{{ element }}
  </a>
</ng-template>

<ng-template #tplName let-element="element" let-row="row">
  {{ element }} / {{ row.variantName }}
</ng-template>

<ng-template #tplCategory let-element="element" let-row="row">
  {{ element[0].name }}
</ng-template>

<ng-template #tplIsOnline let-element="element">
  <app-tag-boolean-status
    [value]="element"
    [trueText]="'online'"
    [falseText]="'offline'"
  ></app-tag-boolean-status>
</ng-template>

<ng-template #tplTimestamp let-element="element">
  {{ element | dateFormat | nullValue }}
</ng-template>

<ng-template #tplImage let-element="element" let-row="row">
  <nz-avatar-group class="d-inline pointer">
    @for (el of element; track $index) {
      <nz-avatar
        nz-tooltip
        [nzTooltipTitle]="el.name"
        [nzSrc]="el.url"
        [nzSize]="'small'"
        (click)="onImageClick(element)"
      ></nz-avatar>
    }
  </nz-avatar-group>
</ng-template>

<ng-template #tplNullValue>
  {{ null | nullValue }}
</ng-template>
