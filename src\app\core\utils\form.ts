import { FormArray, FormGroup } from '@angular/forms';
import { IFileUpload } from '@models/interfaces/file-upload';
import { cloneDeep } from 'lodash';
import { NzUploadFile } from 'ng-zorro-antd/upload';
import { CheckUtils } from './check';
import { ObjectUtils } from './object';

export class FormUtils {
  /**
   * Update Form with object values
   * @param fg
   * @param updateObj
   */
  public static fillUpdateDataForm(fg: FormGroup, updateObj: any): void {
    for (const field in fg.controls) {
      if (ObjectUtils.propertyExists(updateObj, field))
        fg.get(field)?.setValue(
          ObjectUtils.getPropertyByName(updateObj, field),
        );
    }
  }

  /**
   * It takes a form group and an object and fills the form group with the values of the object.
   * @param {FormGroup} fg - FormGroup - the form group that you want to update
   * @param {any} updateObj - The object that will be updated with the form data.
   */
  public static fillUpdateDataNestedForm(fg: FormGroup, updateObj: any) {
    for (const [field, value] of Object.entries(fg.controls)) {
      if (((<unknown>value) as FormGroup).controls)
        this.fillUpdateDataNestedForm(value as FormGroup, updateObj[field]);
      else fg.get(field)!.setValue(updateObj[field]);
    }
  }

  /**
   * It takes a formgroud and object and collect form value (also nested) into object
   * @param form
   * @param object
   */
  public static collectNestedFormData(form: FormGroup, object: any): void {
    for (const field in form.controls) {
      if (((<unknown>field) as FormGroup).controls)
        this.collectNestedFormData(
          (<unknown>field) as FormGroup,
          object[field],
        );
      else object[field] = form.get(field)?.value;
    }
  }

  /**
   * Collect data from the form and assign it to the object
   * @param fg
   * @param object
   */
  public static collectFormData(fg: FormGroup, object: any) {
    for (const field in fg.controls) {
      ObjectUtils.setPropertyByName(object, field, fg.get(field)!.value);
    }
  }

  /**
   * Remove object properties if their values ​​are null
   * @param object
   */
  public static removeObjectNullProperties(object: any) {
    for (var [key, value] of Object.entries(object)) {
      if (
        !CheckUtils.isNullUndefinedOrEmpty(object[key]) &&
        typeof object[key] == 'object'
      ) {
        this.removeObjectNullProperties(object[key]);
      }
      if (CheckUtils.isNullUndefinedOrEmpty(object[key])) {
        ObjectUtils.removePropertyByName(object, key);
      }
    }
  }

  // public static populateImage(
  //   fb: FormGroup,
  //   controlName: string
  // ): NzUploadFile[] {
  //   const image: string = fb.get(controlName).value;
  //   let fileList: NzUploadFile[] = [image].reduce((acc, image) => {
  //     const file: NzUploadFile = {
  //       uid: '1',
  //       name: 'image.name',
  //       status: 'done',
  //       url: image,
  //       thumbUrl: image
  //     };
  //     acc.push(file);
  //     return acc;
  //   }, []);
  //   return cloneDeep(fileList);
  // }

  public static populateImages(
    fb: FormGroup,
    controlName: string,
  ): NzUploadFile[] {
    const images: IFileUpload[] = fb.get(controlName)!.value;
    if (images?.length <= 0) return [];
    let fileList: NzUploadFile[] = images.reduce((acc, image) => {
      const file: NzUploadFile = {
        public_id: image.public_id,
        uid: image.url,
        name: image.name,
        status: 'done',
        url: image.url,
        thumbUrl: image.url,
      };
      acc.push(file);
      return acc;
    }, []);
    return cloneDeep(fileList);
  }

  public static populateVideos(
    fb: FormGroup,
    controlName: string,
  ): NzUploadFile[] {
    const videos: any[] = fb.get(controlName)!.value;
    if (videos?.length <= 0) return [];
    let fileList: NzUploadFile[] = videos.reduce((acc, video) => {
      const file: NzUploadFile = {
        public_id: video.public_id,
        uid: video.url,
        name: video.name,
        status: 'done',
        url: video.url,
        thumbUrl: video.url,
      };
      acc.push(file);
      return acc;
    }, []);
    return cloneDeep(fileList);
  }

  public static populateField(fb: FormGroup, control: string): any[] | any {
    return cloneDeep(fb.get(control)!.value);
  }

  public static smartRemoveFormArray(array: FormArray, index: number): void {
    const groupToRemove = array.at(index);
    const hasValue = Object.values(groupToRemove.value).some(
      (v) => v !== null && v !== undefined && v !== '',
    );
    array.removeAt(index);
    if (hasValue) array.markAsDirty();
  }
}
