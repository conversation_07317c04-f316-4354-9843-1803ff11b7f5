import { Component, inject } from '@angular/core';
import { Router } from '@angular/router';
import { RolePermissionDirective } from '@core/directives/role-permission';
import { roleType } from '@models/enums/role';
import { TranslateModule } from '@ngx-translate/core';
import { PopoverButtonComponent } from '@shared/buttons/popover-button/popover-button';
import { SimpleButtonComponent } from '@shared/buttons/simple-button/simple-button';
import { NzSpaceComponent, NzSpaceItemDirective } from 'ng-zorro-antd/space';

@Component({
  selector: 'app-staff-list-header',
  standalone: true,
  imports: [
    NzSpaceComponent,
    NzSpaceItemDirective,
    SimpleButtonComponent,
    TranslateModule,
    PopoverButtonComponent,
    RolePermissionDirective,
  ],
  templateUrl: './staff-list-header.html',
  styleUrl: './staff-list-header.less',
})
export class StaffListHeader {
  // SERVICES
  private router = inject(Router);

  // ENUMS
  roleType = roleType;

  onCreateStaffClick() {
    this.router.navigateByUrl('/staff/create');
  }
}
