import { Component, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Router } from '@angular/router';
import { trackEvent } from '@aptabase/web';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzExceptionStatusType, NzResultModule } from 'ng-zorro-antd/result';

@Component({
  selector: 'app-error',
  standalone: true,
  imports: [NzButtonModule, NzResultModule, TranslateModule],
  templateUrl: './error.component.html',
  styleUrl: './error.component.less',
  host: { style: 'margin: auto;' },
})
export class ErrorComponent {
  // SERVICES
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private translateService = inject(TranslateService);

  // PROPERTIES
  private returnUrl: string = '/';
  private errorMap: {
    status: NzExceptionStatusType;
    title: string;
    description: string;
  }[] = [
    {
      status: '403',
      title: '403',
      description: this.translateService.instant('ERRORS.403'),
    },
    {
      status: '404',
      title: '404',
      description: this.translateService.instant('ERRORS.404'),
    },
    {
      status: '500',
      title: '500',
      description: this.translateService.instant('ERRORS.500'),
    },
  ];

  protected error = this.errorMap[0];

  constructor() {
    trackEvent('error_page');
    this.route.queryParams.pipe(takeUntilDestroyed()).subscribe((params) => {
      this.error =
        this.errorMap.find((error) => error.status === params['code']) ||
        this.error;
      if (params['returnUrl']) this.returnUrl = params['returnUrl'];
    });
  }

  gotoHome() {
    this.router.navigate(['/']);
  }

  reloadPage() {
    this.router.navigate([this.returnUrl]);
  }
}
