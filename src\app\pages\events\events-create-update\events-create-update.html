<!-- MAIN CONTAINER -->
<div class="container">
  <!-- EVENTS FORM -->
  <form nz-form class="form" [formGroup]="baseForm" nzLayout="vertical">
    <!-- LOADING SPINNER -->
    <nz-spin [nzSpinning]="loading()">
      <!-- EVENTS INFO SECTION -->
      <div nz-row class="w-100" [nzGutter]="16">
        <div nz-col [nzSpan]="14">
          <div nz-row [nzGutter]="16">
            <div nz-col [nzSpan]="24">
              <!-- EVENTS TITLE -->
              <app-input-generic
                [parentForm]="baseForm"
                [controlName]="'title'"
                [label]="'EVENTS.title' | translate"
                [placeholder]="'EVENTS.titlePlaceholder' | translate"
                [minLength]="3"
              ></app-input-generic>
            </div>

            <div nz-col [nzSpan]="24">
              <!-- EVENTS CONTENT -->
              <app-input-textarea
                [parentForm]="baseForm"
                [controlName]="'content'"
                [label]="'EVENTS.content' | translate"
                [placeholder]="'EVENTS.contentPlaceholder' | translate"
                [minLength]="10"
                [size]="{ minRows: 9, maxRows: 40 }"
              ></app-input-textarea>
            </div>
          </div>
        </div>
        <div nz-col [nzSpan]="10">
          <div nz-row [nzGutter]="16">
            <div nz-col [nzSpan]="24">
              <!-- EVENTS STATUS -->
              <app-input-checkbox
                [parentForm]="baseForm"
                [controlName]="'isOnline'"
                [label]="'EVENTS.eventStatus' | translate"
                [optionList]="showEventStatus()"
                [name]="'EVENTS.showOnline' | translate"
              ></app-input-checkbox>
            </div>

            <div nz-col [nzSpan]="24">
              <!-- EVENTS DATE -->
              <app-input-single-datepicker
                [parentForm]="baseForm"
                [controlName]="'date'"
                [label]="'EVENTS.date'"
                [placeholder]="'EVENTS.datePlaceholder'"
                [labelPosition]="'top'"
                [nzFormat]="'yyyy-MM-dd'"
                [style]="{ maxWidth: '200px' }"
              ></app-input-single-datepicker>
            </div>

            <div nz-col [nzSpan]="24">
              <!-- EVENTS IMAGE UPLOAD -->
              <app-input-image-upload
                [parentForm]="baseForm"
                [controlName]="'images'"
                [label]="'image'"
                [fileType]="'image/png,image/jpeg'"
                [showAddButton]="FileList?.length === 0"
                [imageContainerSize]="{ width: '200px', height: '200px' }"
                [maxImages]="1"
                [fileList]="FileList || []"
                [imageList]="ImageList || []"
              ></app-input-image-upload>
            </div>
          </div>
        </div>
      </div>
    </nz-spin>
  </form>
</div>

<!-- ACTION BUTTONS TEMPLATE -->
<ng-template #tplButton>
  <!-- SAVE/UPDATE BUTTON -->
  <button
    nz-button
    nzType="primary"
    (click)="onDataSaveClick()"
    [disabled]="!isValidForm() || loading()"
  >
    {{ saveButtonTitle() | translate }}
    @if (crudMode() === crudActionType.create) {
      <i nz-icon nzType="client-ui:plus"></i>
    } @else {
      <i nz-icon nzType="save"></i>
    }
  </button>
</ng-template>
